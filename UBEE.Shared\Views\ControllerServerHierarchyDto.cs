﻿public class ControllerServerHierarchyDto
{
    public Guid ControllerServerId { get; set; }
    public string ControllerServerName { get; set; }
    public int MaxControllers { get; set; }
    public int MaxSensors { get; set; }
    public string GeographicZone { get; set; }
    public string CommercialCondition { get; set; }
    public string TriggerType { get; set; }
    public string ActionType { get; set; }
    public string EventType { get; set; }
    public string ControllerServerStatus { get; set; }
    public Guid IdLicence { get; set; }
    public DateTime ControllerServerCreatedAt { get; set; }
    public DateTime ControllerServerLastUpdatedAt { get; set; }

    public Guid LicenceId { get; set; }
    public string LicenceName { get; set; }
    public string LicenceDescription { get; set; }

    public Guid? RelationId { get; set; }
    public Guid? IdController { get; set; }

    public Guid? ControllerId { get; set; }
    public string ControllerHostName { get; set; }
    public string ControllerModel { get; set; }
    public string ControllerSerialNumber { get; set; }
    public string ControllerMacAddress { get; set; }
    public string ControllerIpAddress { get; set; }
    public string ControllerBaseTopic { get; set; }
    public bool ControllerState { get; set; }
    public DateTime? ControllerLastConnection { get; set; }

    public Guid? ClientId { get; set; }
    public string ClientName { get; set; }
}