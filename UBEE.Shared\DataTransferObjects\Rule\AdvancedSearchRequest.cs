﻿using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.MODELS.DataSource.DTOs
{
    public class AdvancedSearchRequest
    {
        public string SearchTerm { get; set; } = string.Empty;
        public bool? IncludeInactive { get; set; } = false;
        public int? Page { get; set; } = 1;
        public int? PageSize { get; set; } = 10;
        public List<Sorting>? SortParams { get; set; }
        public List<Filtering.WhereParams>? FilterParams { get; set; }
    }
}