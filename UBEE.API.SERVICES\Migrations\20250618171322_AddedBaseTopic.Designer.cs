﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using UBEE.MODELS.DataSource;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    [DbContext(typeof(UBEEContext))]
    [Migration("20250618171322_AddedBaseTopic")]
    partial class AddedBaseTopic
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.17")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Capteur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Endpoint")
                        .HasColumnType("int");

                    b.Property<string>("FriendlyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("HomeId")
                        .HasColumnType("int");

                    b.Property<Guid>("IdTypeCapteur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("IeeeAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastSeen")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Manufacturer")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModelIdentifier")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NetworkAddress")
                        .HasColumnType("int");

                    b.Property<int>("NodeId")
                        .HasColumnType("int");

                    b.Property<string>("Protocol")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecurityClasses")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdTypeCapteur");

                    b.ToTable("Capteur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Client", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdOrganisation")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte[]>("LogoOrganisation")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("NomComplet")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NombreEquipementsActif")
                        .HasColumnType("int");

                    b.Property<int>("NombreEquipementsInactif")
                        .HasColumnType("int");

                    b.Property<int>("NombreLocaux")
                        .HasColumnType("int");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PasswordConfirmed")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdOrganisation");

                    b.ToTable("Client");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Controller", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Controller");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerController", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdController")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdControllerServeur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdController");

                    b.HasIndex("IdControllerServeur");

                    b.ToTable("ControllerServerController");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdControllerServeur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdRules")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdControllerServeur");

                    b.HasIndex("IdRules");

                    b.ToTable("ControllerServerRule");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServeur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdLicence")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("IpAdresse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdLicence");

                    b.ToTable("ControllerServeur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Facture", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdLicence")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Total")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("IdLicence");

                    b.ToTable("Facture");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Licence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateOnly>("DateDebut")
                        .HasColumnType("date");

                    b.Property<DateOnly>("DateFin")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdClient")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdClient");

                    b.ToTable("Licence");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Local", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Architecture2DImage")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("BaseTopic")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CapacitePersonnes")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Etage")
                        .HasColumnType("int");

                    b.Property<Guid>("IdSite")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("ImageLocal")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NombreCapteurs")
                        .HasColumnType("int");

                    b.Property<double>("Surface")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("IdSite");

                    b.ToTable("Local");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Log", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdCapteur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdController")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdLocal")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdTransaction")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdTransaction");

                    b.ToTable("Log");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Organisation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Organisation");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdRule")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdTag")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdRule");

                    b.HasIndex("IdTag");

                    b.ToTable("RuleTag");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdRule")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdTransaction")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdRule");

                    b.HasIndex("IdTransaction");

                    b.ToTable("RuleTransaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Rules", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<string>("RawData")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Rules");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.SensorReading", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("Battery")
                        .HasColumnType("int");

                    b.Property<int?>("BatteryLevel")
                        .HasColumnType("int");

                    b.Property<bool?>("Contact")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Current")
                        .HasColumnType("float");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Energy")
                        .HasColumnType("float");

                    b.Property<double?>("Humidity")
                        .HasColumnType("float");

                    b.Property<Guid>("IdCapteur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("Illuminance")
                        .HasColumnType("float");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("LinkQuality")
                        .HasColumnType("int");

                    b.Property<bool?>("Occupancy")
                        .HasColumnType("bit");

                    b.Property<double?>("Power")
                        .HasColumnType("float");

                    b.Property<bool?>("Tamper")
                        .HasColumnType("bit");

                    b.Property<double?>("Temperature")
                        .HasColumnType("float");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<double?>("Value")
                        .HasColumnType("float");

                    b.Property<double?>("Voltage")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("IdCapteur");

                    b.ToTable("SensorReading");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ContactDeSite")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdClient")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("Images")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Pays")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Rue")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TelephoneSurSite")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Ville")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdClient");

                    b.ToTable("Site");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Tag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Tag");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdCapteur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdController")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdLocal")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("InControl")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdCapteur");

                    b.HasIndex("IdController");

                    b.HasIndex("IdLocal");

                    b.ToTable("Transaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.TypeCapteur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("TypeCapteur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.TypeLocal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdLocal")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdLocal");

                    b.ToTable("TypeLocal");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Variables", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdTypeCapteur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NomTechnique")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdTypeCapteur");

                    b.ToTable("Variables");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Capteur", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.TypeCapteur", "TypeCapteur")
                        .WithMany("Capteurs")
                        .HasForeignKey("IdTypeCapteur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TypeCapteur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Client", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Organisation", "Organisation")
                        .WithMany("Clients")
                        .HasForeignKey("IdOrganisation")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerController", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Controller", "Controller")
                        .WithMany("ControllerServerControllers")
                        .HasForeignKey("IdController")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.ControllerServeur", "ControllerServeur")
                        .WithMany("ControllerServerControllers")
                        .HasForeignKey("IdControllerServeur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Controller");

                    b.Navigation("ControllerServeur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerRule", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.ControllerServeur", "ControllerServeur")
                        .WithMany("ControllerServerRules")
                        .HasForeignKey("IdControllerServeur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Rules", "Rules")
                        .WithMany("ControllerServerRules")
                        .HasForeignKey("IdRules")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ControllerServeur");

                    b.Navigation("Rules");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServeur", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Licence", "Licence")
                        .WithMany("ControllerServeurs")
                        .HasForeignKey("IdLicence")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Licence");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Facture", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Licence", "Licence")
                        .WithMany("Factures")
                        .HasForeignKey("IdLicence")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Licence");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Licence", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Client", "Client")
                        .WithMany("Licences")
                        .HasForeignKey("IdClient")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Local", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Site", "Site")
                        .WithMany("Locals")
                        .HasForeignKey("IdSite")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Log", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Transaction", "Transaction")
                        .WithMany("Logs")
                        .HasForeignKey("IdTransaction")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTag", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Rules", "Rule")
                        .WithMany("RuleTags")
                        .HasForeignKey("IdRule")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Tag", "Tag")
                        .WithMany("RuleTags")
                        .HasForeignKey("IdTag")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rule");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTransaction", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Rules", "Rule")
                        .WithMany("RuleTransactions")
                        .HasForeignKey("IdRule")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Transaction", "Transaction")
                        .WithMany("RuleTransactions")
                        .HasForeignKey("IdTransaction")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rule");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.SensorReading", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Capteur", "Capteur")
                        .WithMany("SensorReadings")
                        .HasForeignKey("IdCapteur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capteur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Site", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Client", "Client")
                        .WithMany("Sites")
                        .HasForeignKey("IdClient")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Transaction", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Capteur", "Capteur")
                        .WithMany("Transactions")
                        .HasForeignKey("IdCapteur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Controller", "Controller")
                        .WithMany("Transactions")
                        .HasForeignKey("IdController")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Local", "Local")
                        .WithMany("Transactions")
                        .HasForeignKey("IdLocal")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capteur");

                    b.Navigation("Controller");

                    b.Navigation("Local");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.TypeLocal", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Local", "Local")
                        .WithMany("TypeLocals")
                        .HasForeignKey("IdLocal")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Local");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Variables", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.TypeCapteur", "TypeCapteur")
                        .WithMany("Variables")
                        .HasForeignKey("IdTypeCapteur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TypeCapteur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Capteur", b =>
                {
                    b.Navigation("SensorReadings");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Client", b =>
                {
                    b.Navigation("Licences");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Controller", b =>
                {
                    b.Navigation("ControllerServerControllers");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServeur", b =>
                {
                    b.Navigation("ControllerServerControllers");

                    b.Navigation("ControllerServerRules");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Licence", b =>
                {
                    b.Navigation("ControllerServeurs");

                    b.Navigation("Factures");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Local", b =>
                {
                    b.Navigation("Transactions");

                    b.Navigation("TypeLocals");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Organisation", b =>
                {
                    b.Navigation("Clients");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Rules", b =>
                {
                    b.Navigation("ControllerServerRules");

                    b.Navigation("RuleTags");

                    b.Navigation("RuleTransactions");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Site", b =>
                {
                    b.Navigation("Locals");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Tag", b =>
                {
                    b.Navigation("RuleTags");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Transaction", b =>
                {
                    b.Navigation("Logs");

                    b.Navigation("RuleTransactions");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.TypeCapteur", b =>
                {
                    b.Navigation("Capteurs");

                    b.Navigation("Variables");
                });
#pragma warning restore 612, 618
        }
    }
}
