using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Presentation.Models;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/tag-assignment")]
[ApiController]
public class TagAssignmentController : BaseController<TagAssignment>
{
    private readonly ITagAssignmentService _service;
    
    public TagAssignmentController(ITagAssignmentService baseService) : base(baseService)
    {
        this._service = baseService as ITagAssignmentService;
        this.UnwantedProperties += "";
        IncludesProperties = "Tag";
    }

    [HttpGet("target/{targetType}/{targetId}")]
    public async Task<IActionResult> GetByTarget(TargetType targetType, Guid targetId)
    {
        var assignments = await _service.Get(ta => ta.TargetType == targetType && ta.TargetId == targetId, includeProperties: IncludesProperties);
        return Ok(assignments.MarshelList(WantedProperties, UnwantedProperties));
    }

    [HttpGet("tag/{tagId}")]
    public async Task<IActionResult> GetByTag(Guid tagId)
    {
        var assignments = await _service.Get(ta => ta.IdTag == tagId, includeProperties: IncludesProperties);
        return Ok(assignments.MarshelList(WantedProperties, UnwantedProperties));
    }
}