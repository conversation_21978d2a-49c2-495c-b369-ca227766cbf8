﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class RulesTransactionConfiguration : IEntityTypeConfiguration<RuleTransaction>
{
    public void Configure(EntityTypeBuilder<RuleTransaction> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.Rule)
        .WithMany(s => s.RuleTransactions)
        .HasForeignKey(s => s.IdRule);

        builder.HasOne(s => s.Transaction)
        .WithMany(s => s.RuleTransactions)
        .HasForeignKey(s => s.IdTransaction);
    }
}
