﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class RulesTagConfiguration : IEntityTypeConfiguration<RuleTag>
{
    public void Configure(EntityTypeBuilder<RuleTag> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.Rule)
        .WithMany(s => s.RuleTags)
        .HasForeignKey(s => s.IdRule);

        builder.HasOne(s => s.Tag)
        .WithMany(s => s.RuleTags)
        .HasForeignKey(s => s.IdTag);
    }
}