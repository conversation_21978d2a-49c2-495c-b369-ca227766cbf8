﻿namespace UBEE.MODELS.DataSource.DTOs
{
    public class RuleClientHierarchyDto
    {
        public Guid RuleId { get; set; }
        public bool? RuleEnabled { get; set; }
        public int? RulePriority { get; set; }

        // Client Information
        public Guid ClientId { get; set; }
        public Guid ClientOrganisationId { get; set; }
        public string? ClientRC { get; set; }
        public string? ClientRegion { get; set; }
        public string? ClientSIREN { get; set; }
        public string? ClientSIRET { get; set; }
        public int? ClientActiveEquipment { get; set; }

        // Site Information
        public Guid SiteId { get; set; }
        public string? SiteName { get; set; }
        public string? SiteEmail { get; set; }
        public string? SiteDescription { get; set; }
        public string? SiteContact { get; set; }
        public string? SiteManager { get; set; }
        public string? SitePhoneNumber { get; set; }
        public string? SiteStatus { get; set; }
        public string? SiteAddressComplement { get; set; }
        public string? SiteAddress { get; set; }
        public int? SiteEmployeeCount { get; set; }
        public string? SiteGrade { get; set; }
        public double? SiteLatitude { get; set; } // Nullable
        public int? SiteLocalCount { get; set; }
        public double? SiteLongitude { get; set; } // Nullable

        // Local Information
        public Guid LocalId { get; set; }
        public DateTime LocalCreatedAt { get; set; }
        public string? LocalCreatedBy { get; set; }
        public DateTime? LocalLastUpdatedAt { get; set; } // Nullable
        public string? LocalLastUpdatedBy { get; set; }
        public DateTime? LocalDeletedAt { get; set; } // Nullable
        public string? LocalDeletedBy { get; set; }
        public int? LocalCapacity { get; set; }
        public int? LocalFloor { get; set; }
        public string? LocalName { get; set; }
        public int? LocalSensorCount { get; set; }
        public double? LocalLatitude { get; set; } // Nullable
        public double? LocalLongitude { get; set; } // Nullable

    }
}