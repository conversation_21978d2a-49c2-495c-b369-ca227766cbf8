﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedtolog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local");

            migrationBuilder.RenameColumn(
                name: "IdController",
                table: "Log",
                newName: "ControllerId");

            migrationBuilder.RenameColumn(
                name: "IdCapteur",
                table: "Log",
                newName: "CapteurId");

            migrationBuilder.CreateIndex(
                name: "IX_Log_CapteurId",
                table: "Log",
                column: "CapteurId");

            migrationBuilder.CreateIndex(
                name: "IX_Log_ControllerId",
                table: "Log",
                column: "ControllerId");

            migrationBuilder.AddForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local",
                column: "IdSite",
                principalTable: "Site",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Capteur_CapteurId",
                table: "Log",
                column: "CapteurId",
                principalTable: "Capteur",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Controller_ControllerId",
                table: "Log",
                column: "ControllerId",
                principalTable: "Controller",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local");

            migrationBuilder.DropForeignKey(
                name: "FK_Log_Capteur_CapteurId",
                table: "Log");

            migrationBuilder.DropForeignKey(
                name: "FK_Log_Controller_ControllerId",
                table: "Log");

            migrationBuilder.DropIndex(
                name: "IX_Log_CapteurId",
                table: "Log");

            migrationBuilder.DropIndex(
                name: "IX_Log_ControllerId",
                table: "Log");

            migrationBuilder.RenameColumn(
                name: "ControllerId",
                table: "Log",
                newName: "IdController");

            migrationBuilder.RenameColumn(
                name: "CapteurId",
                table: "Log",
                newName: "IdCapteur");

            migrationBuilder.AddForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local",
                column: "IdSite",
                principalTable: "Site",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
