﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedlatestattributs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Client_Client_ClientId",
                table: "Client");

            migrationBuilder.DropForeignKey(
                name: "FK_ControllerServeur_Licence_IdLicence",
                table: "ControllerServeur");

            migrationBuilder.DropIndex(
                name: "IX_Client_ClientId",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "Nom",
                table: "ControllerServeur",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "Nom",
                table: "Controller",
                newName: "SerialNumber");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "Controller",
                newName: "State");

            migrationBuilder.RenameColumn(
                name: "ClientId",
                table: "Client",
                newName: "RecursiveClientId");

            migrationBuilder.AlterColumn<Guid>(
                name: "IdLicence",
                table: "ControllerServeur",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<string>(
                name: "HostName",
                table: "Controller",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateOnly>(
                name: "InstallationDate",
                table: "Controller",
                type: "date",
                nullable: false,
                defaultValue: new DateOnly(1, 1, 1));

            migrationBuilder.AddColumn<string>(
                name: "IpAddress",
                table: "Controller",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "LastConnection",
                table: "Controller",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "MacAddress",
                table: "Controller",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Model",
                table: "Controller",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "SIRET",
                table: "Client",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "SIREN",
                table: "Client",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "RC",
                table: "Client",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "Patente",
                table: "Client",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "IF",
                table: "Client",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "ICE",
                table: "Client",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<byte[]>(
                name: "ClientLogo",
                table: "Client",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ControllerServeur_Licence_IdLicence",
                table: "ControllerServeur",
                column: "IdLicence",
                principalTable: "Licence",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ControllerServeur_Licence_IdLicence",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "HostName",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "InstallationDate",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "IpAddress",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "LastConnection",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "MacAddress",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "Model",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "ClientLogo",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "ControllerServeur",
                newName: "Nom");

            migrationBuilder.RenameColumn(
                name: "State",
                table: "Controller",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "SerialNumber",
                table: "Controller",
                newName: "Nom");

            migrationBuilder.RenameColumn(
                name: "RecursiveClientId",
                table: "Client",
                newName: "ClientId");

            migrationBuilder.AlterColumn<Guid>(
                name: "IdLicence",
                table: "ControllerServeur",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SIRET",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SIREN",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "RC",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Patente",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "IF",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ICE",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Client_ClientId",
                table: "Client",
                column: "ClientId");

            migrationBuilder.AddForeignKey(
                name: "FK_Client_Client_ClientId",
                table: "Client",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ControllerServeur_Licence_IdLicence",
                table: "ControllerServeur",
                column: "IdLicence",
                principalTable: "Licence",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
