﻿using Microsoft.EntityFrameworkCore;
using System.Net.Http.Headers;
using UBEE.API.SERVICES.Services;
using UBEE.Contracts;
using UBEE.Contracts.Repository;
using UBEE.Contracts.Repository.Repositories;
using UBEE.Infrastructure.Repository;
using UBEE.LoggerService;
using UBEE.MODELS.DataSource;
using UBEE.Repository.Repository;
using UBEE.Repository.Repository.Implementation;
using UBEE.Service;
using UBEE.Service.Contracts;
public static class ServiceExtensions
{
    public static void ConfigureCors(this IServiceCollection services)
    {
        services.AddCors(options =>
        {
            options.AddPolicy("CorsPolicy", builder =>
            builder.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
        });
    }

    public static void ConfigureIISIntegration(this IServiceCollection services)
    {
        services.Configure<IISOptions>(options =>
        {
        });
    }

    public static void ConfigureSqlContext(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<UBEEContext>(options => options.UseSqlServer(configuration.GetConnectionString("sqlConnection")));
        services.AddScoped<UBEEContext>();
    }


    public static void ConfigureLoggerService(this IServiceCollection services)
    {
        services.AddSingleton<ILoggerManager, LoggerManager>();
    }
    public static void ConfigureRepositoryManager(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IRepositoryManager, RepositoryManager>();
        services.AddScoped<IRulesRepository, RulesRepository>();
        services.AddScoped(typeof(IBaseService<>), typeof(BaseService<>));
        services.AddScoped<IClientService, ClientService>();
        services.AddScoped<ILocalRepository, LocalRepository>();
        services.AddScoped<IOrganisationService, OrganisationService>();
        services.AddScoped<ILicenceService, LicenceService>();
        services.AddScoped<IFactureService, FactureService>();
        services.AddScoped<ISiteService, SiteService>();
        services.AddScoped<ILocalService, LocalService>();
        services.AddScoped<ILogService, LogService>();
        services.AddScoped<ITypeLocalService, TypeLocalService>();
        services.AddScoped<ITransactionService, TransactionService>();
        services.AddScoped<IControllerServeurService, ControllerServeurService>();
        services.AddScoped<IControllerServerControllerService, ControllerServerControllerService>();
        services.AddScoped<IControllerService, ControllerService>();
        services.AddScoped<ICapteurService, CapteurService>();
        services.AddScoped<IControllerServerRuleService, ControllerServerRuleService>();
        services.AddScoped<IRulesService, RulesService>();
        services.AddScoped<IRuleTransactionService, RuleTransactionService>();
        services.AddScoped<IRuleTagService, RuleTagService>();
        services.AddScoped<ITagService, TagService>();
        services.AddScoped<ISensorReadingService, SensorReadingService>();
        services.AddScoped<IVariablesService, VariablesService>();
        services.AddScoped<ITypeCapteurService, TypeCapteurService>();
        services.AddScoped<IOptionService, OptionService>();
        services.AddScoped<ILicenceService, LicenceService>();
        services.AddScoped<ILicenceOptionService, LicenceOptionService>();
        services.AddScoped<ISubscriptionService, SubscriptionService>();
        services.AddScoped<ISubscribedOptionsService, SubscriptionOptionsService>();
        services.AddScoped<IImageService, ImageService>();
        services.AddScoped<ITagAssignmentService, TagAssignmentService>();

        // Remove the duplicate line and use only this one:
        services.AddHttpClient<ITextSummarizationService, TextSummarizationService>(client =>
        {
            client.Timeout = TimeSpan.FromMinutes(2);
            // Headers will be set per request in the service
        });
    }
    public static void ConfigureServiceManager(this IServiceCollection services)
    {
        services.AddScoped<IServiceManager, ServiceManager>();
    }

    //public static IMvcBuilder AddCustomCSVFormatter(this IMvcBuilder builder) =>
    //    builder.AddMvcOptions(config =>
    //        config.OutputFormatters.Add(new CsvOutputFormatter()));

    public static void ConfigureEmailService(this IServiceCollection services, IConfiguration configuration)
    {
        //var emailConfig = configuration
        //.GetSection("EmailConfiguration")
        //.Get<EmailConfiguration>();
        //services.AddSingleton(emailConfig);

        //services.AddScoped<EmailService.IEmailSender, EmailSender>();
    }

    public static void ConfigureWorkersService(this IServiceCollection services)
    {
    }
}

