﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class newsiteandlocal : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local");

            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "IsActif",
                table: "Site");

            migrationBuilder.RenameColumn(
                name: "Street",
                table: "Site",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "Country",
                table: "Site",
                newName: "Manager");

            migrationBuilder.RenameColumn(
                name: "City",
                table: "Site",
                newName: "Email");

            migrationBuilder.AddColumn<string>(
                name: "AddressComplement",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Adress",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "EmployeesCount",
                table: "Site",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Grade",
                table: "Site",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<double>(
                name: "Latitude",
                table: "Site",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<int>(
                name: "LocalsCount",
                table: "Site",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<double>(
                name: "Longtitude",
                table: "Site",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Surface",
                table: "Site",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Latitude",
                table: "Local",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local",
                column: "IdSite",
                principalTable: "Site",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local");

            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "AddressComplement",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Adress",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "EmployeesCount",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Grade",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "LocalsCount",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Longtitude",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Surface",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "ActiveEquipment",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Brand",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "BusinessSector",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "City",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ClientStatus",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanyCreationDate",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanySize",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanyType",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactAddress",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactEmail",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactName",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Filiale",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ICE",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "IF",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "InactiveEquipment",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "LegalForm",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Patente",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "RecursiveClientId",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "Site",
                newName: "Street");

            migrationBuilder.RenameColumn(
                name: "Manager",
                table: "Site",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "Email",
                table: "Site",
                newName: "City");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "Local",
                newName: "Nom");

            migrationBuilder.RenameColumn(
                name: "Longtitude",
                table: "Local",
                newName: "Surface");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "Client",
                newName: "Telephone");

            migrationBuilder.RenameColumn(
                name: "SIRET",
                table: "Client",
                newName: "PasswordConfirmed");

            migrationBuilder.RenameColumn(
                name: "SIREN",
                table: "Client",
                newName: "Password");

            migrationBuilder.RenameColumn(
                name: "Region",
                table: "Client",
                newName: "NomComplet");

            migrationBuilder.RenameColumn(
                name: "RC",
                table: "Client",
                newName: "Email");

            migrationBuilder.AddColumn<bool>(
                name: "IsActif",
                table: "Site",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "CapacitePersonnes",
                table: "Local",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Local",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "IpAdresse",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsEnabled",
                table: "Client",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<byte[]>(
                name: "LogoClient",
                table: "Client",
                type: "varbinary(max)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<int>(
                name: "NombreEquipementsActif",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NombreEquipementsInactif",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NombreLocaux",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local",
                column: "IdSite",
                principalTable: "Site",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
