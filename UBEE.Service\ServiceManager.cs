﻿using UBEE.Contracts;
using UBEE.Contracts.Repository;
using UBEE.Service.Contracts;

namespace UBEE.Service;

public sealed class ServiceManager : IServiceManager
{
    public ServiceManager(IRepositoryManager repositoryManager, ILoggerManager logger)
    {
        //_licenceService = new Lazy<ILicenceService>(() => new LicenceService(repositoryManager, logger, mapper));
        //_deviceService = new Lazy<IDeviceService>(() => new DeviceService(repositoryManager, logger));
    }
}