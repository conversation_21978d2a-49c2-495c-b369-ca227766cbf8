﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class AddedConnectionBetweenClientAndLicense : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "IdClient",
                table: "Licence",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Licence_IdClient",
                table: "Licence",
                column: "IdClient");

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropIndex(
                name: "IX_Licence_IdClient",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "IdClient",
                table: "Licence");
        }
    }
}
