﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedsubscription : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropIndex(
                name: "IX_Licence_IdClient",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "DateDebut",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "DateFin",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "IdClient",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "Licence");

            migrationBuilder.AddColumn<Guid>(
                name: "SubscriptionId",
                table: "Facture",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Subscription",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DateDebut = table.Column<DateOnly>(type: "date", nullable: true),
                    DateFin = table.Column<DateOnly>(type: "date", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClientId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    LicenceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentFrequency = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subscription", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Subscription_Client_ClientId",
                        column: x => x.ClientId,
                        principalTable: "Client",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Subscription_Licence_LicenceId",
                        column: x => x.LicenceId,
                        principalTable: "Licence",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Facture_SubscriptionId",
                table: "Facture",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_Subscription_ClientId",
                table: "Subscription",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_Subscription_LicenceId",
                table: "Subscription",
                column: "LicenceId");

            migrationBuilder.AddForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture");

            migrationBuilder.DropTable(
                name: "Subscription");

            migrationBuilder.DropIndex(
                name: "IX_Facture_SubscriptionId",
                table: "Facture");

            migrationBuilder.DropColumn(
                name: "DeviceType",
                table: "TypeCapteur");

            migrationBuilder.DropColumn(
                name: "DisplayName",
                table: "TypeCapteur");

            migrationBuilder.DropColumn(
                name: "SubscriptionId",
                table: "Facture");

            migrationBuilder.RenameColumn(
                name: "Key",
                table: "Variables",
                newName: "Nom");

            migrationBuilder.RenameColumn(
                name: "Topic",
                table: "TypeCapteur",
                newName: "Capabilities");

            migrationBuilder.AlterColumn<string>(
                name: "Actions",
                table: "Variables",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Readable",
                table: "Variables",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateOnly>(
                name: "DateDebut",
                table: "Licence",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<DateOnly>(
                name: "DateFin",
                table: "Licence",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "IdClient",
                table: "Licence",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "Licence",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Licence_IdClient",
                table: "Licence",
                column: "IdClient");

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id");
        }
    }
}
