﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/tag")]
[ApiController]
public class TagController : BaseController<Tag>
{
    private readonly ITagService _service;
    public TagController(ITagService baseService) : base(baseService)
    {
        this._service = baseService as ITagService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}

