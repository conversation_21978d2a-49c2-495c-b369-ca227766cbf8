﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Facture: AuditEntity
{
    public double Total { get; set; }

    public string Status { get; set; } // e.g. "Paid", "Pending", "Cancelled"
    public Guid SubscriptionId { get; set; } 
    public Subscription Subscription { get; set; }

    public Guid IdLicence { get; set; }

    public Licence Licence { get; set; }
}
