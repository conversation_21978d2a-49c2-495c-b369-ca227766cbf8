﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedattributstositeandlocal : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local");

            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "IsActif",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "BaseTopic",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "CapacitePersonnes",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "Ip<PERSON><PERSON><PERSON>",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "IsEnabled",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "LogoClient",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreEquipementsActif",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreEquipementsInactif",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreLocaux",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "Street",
                table: "Site",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "Country",
                table: "Site",
                newName: "Manager");

            migrationBuilder.RenameColumn(
                name: "City",
                table: "Site",
                newName: "Email");

            migrationBuilder.RenameColumn(
                name: "Telephone",
                table: "Client",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "PasswordConfirmed",
                table: "Client",
                newName: "SIRET");

            migrationBuilder.RenameColumn(
                name: "Password",
                table: "Client",
                newName: "SIREN");

            migrationBuilder.RenameColumn(
                name: "NomComplet",
                table: "Client",
                newName: "Region");

            migrationBuilder.RenameColumn(
                name: "Email",
                table: "Client",
                newName: "RC");

            migrationBuilder.AddColumn<string>(
                name: "AddressComplement",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Adress",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "EmployeesCount",
                table: "Site",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Grade",
                table: "Site",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<double>(
                name: "Latitude",
                table: "Site",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<int>(
                name: "LocalsCount",
                table: "Site",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<double>(
                name: "Longtitude",
                table: "Site",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Surface",
                table: "Site",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Latitude",
                table: "Local",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<int>(
                name: "ActiveEquipment",
                table: "Client",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Brand",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "BusinessSector",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ClientStatus",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateOnly>(
                name: "CompanyCreationDate",
                table: "Client",
                type: "date",
                nullable: false,
                defaultValue: new DateOnly(1, 1, 1));

            migrationBuilder.AddColumn<string>(
                name: "CompanySize",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CompanyType",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContactAddress",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContactEmail",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContactName",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Filiale",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ICE",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "IF",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "InactiveEquipment",
                table: "Client",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LegalForm",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Patente",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<Guid>(
                name: "RecursiveClientId",
                table: "Client",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local",
                column: "IdSite",
                principalTable: "Site",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local");

            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "AddressComplement",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Adress",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "EmployeesCount",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Grade",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "LocalsCount",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Longtitude",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Surface",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "ActiveEquipment",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Brand",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "BusinessSector",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "City",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ClientStatus",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanyCreationDate",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanySize",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanyType",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactAddress",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactEmail",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactName",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Filiale",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ICE",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "IF",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "InactiveEquipment",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "LegalForm",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Patente",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "RecursiveClientId",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "Site",
                newName: "Street");

            migrationBuilder.RenameColumn(
                name: "Manager",
                table: "Site",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "Email",
                table: "Site",
                newName: "City");

            migrationBuilder.RenameColumn(
                name: "SIREN",
                table: "Client",
                newName: "Password");

            migrationBuilder.RenameColumn(
                name: "Region",
                table: "Client",
                newName: "NomComplet");

            migrationBuilder.RenameColumn(
                name: "RC",
                table: "Client",
                newName: "Email");

            migrationBuilder.AddColumn<bool>(
                name: "IsActif",
                table: "Site",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Local_Site_IdSite",
                table: "Local",
                column: "IdSite",
                principalTable: "Site",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
