﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class changedpricetypedouble : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Facture_Licence_IdLicence",
                table: "Facture");

            migrationBuilder.DropForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Facture",
                table: "Facture");

            migrationBuilder.RenameTable(
                name: "Facture",
                newName: "Factures");

            migrationBuilder.RenameIndex(
                name: "IX_Facture_SubscriptionId",
                table: "Factures",
                newName: "IX_Factures_SubscriptionId");

            migrationBuilder.RenameIndex(
                name: "IX_Facture_IdLicence",
                table: "Factures",
                newName: "IX_Factures_IdLicence");

            migrationBuilder.AlterColumn<float>(
                name: "Price",
                table: "Subscription",
                type: "real",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateOnly>(
                name: "InstallationDate",
                table: "Controller",
                type: "date",
                nullable: true,
                oldClrType: typeof(DateOnly),
                oldType: "date");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Factures",
                table: "Factures",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Factures_Licence_IdLicence",
                table: "Factures",
                column: "IdLicence",
                principalTable: "Licence",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Factures_Subscription_SubscriptionId",
                table: "Factures",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Factures_Licence_IdLicence",
                table: "Factures");

            migrationBuilder.DropForeignKey(
                name: "FK_Factures_Subscription_SubscriptionId",
                table: "Factures");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Factures",
                table: "Factures");

            migrationBuilder.RenameTable(
                name: "Factures",
                newName: "Facture");

            migrationBuilder.RenameIndex(
                name: "IX_Factures_SubscriptionId",
                table: "Facture",
                newName: "IX_Facture_SubscriptionId");

            migrationBuilder.RenameIndex(
                name: "IX_Factures_IdLicence",
                table: "Facture",
                newName: "IX_Facture_IdLicence");

            migrationBuilder.AlterColumn<decimal>(
                name: "Price",
                table: "Subscription",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(float),
                oldType: "real",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateOnly>(
                name: "InstallationDate",
                table: "Controller",
                type: "date",
                nullable: false,
                defaultValue: new DateOnly(1, 1, 1),
                oldClrType: typeof(DateOnly),
                oldType: "date",
                oldNullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Facture",
                table: "Facture",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Facture_Licence_IdLicence",
                table: "Facture",
                column: "IdLicence",
                principalTable: "Licence",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
