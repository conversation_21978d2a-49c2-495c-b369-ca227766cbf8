﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedattributstypeCapteur : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IpAdresse",
                table: "ControllerServeur");

            migrationBuilder.AddColumn<string>(
                name: "IpAdresse",
                table: "Controller",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IpAdresse",
                table: "Controller");

            migrationBuilder.AddColumn<string>(
                name: "IpAdresse",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}
