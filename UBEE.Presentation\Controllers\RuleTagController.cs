﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/rule-tag")]
[ApiController]
public class RuleTagController : BaseController<RuleTag>
{
    private readonly IRuleTagService _service;
    public RuleTagController(IRuleTagService baseService) : base(baseService)
    {
        this._service = baseService as IRuleTagService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}

