﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class SiteConfiguration : IEntityTypeConfiguration<Site>
{
    public void Configure(EntityTypeBuilder<Site> builder)
    {
        builder.HasKey(s => s.Id);

        //builder.HasMany(s => s.Locals)
        //    .WithOne(s => s.Site)
        //    .HasForeignKey(s => s.IdSite)
        //    .OnDelete(DeleteBehavior.Restrict) ;

        builder.HasOne(s => s.Client)
        .WithMany()
        .HasForeignKey(s => s.ClientId);
    }
}