﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class AddeddisplayName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture");

            migrationBuilder.DropForeignKey(
                name: "FK_Subscription_Client_ClientId",
                table: "Subscription");

            migrationBuilder.AlterColumn<Guid>(
                name: "SubscriptionId",
                table: "Facture",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DisplayName",
                table: "Capteur",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Subscription_Client_ClientId",
                table: "Subscription",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture");

            migrationBuilder.DropForeignKey(
                name: "FK_Subscription_Client_ClientId",
                table: "Subscription");

            migrationBuilder.DropTable(
                name: "ClientActiveControllerServers");

            migrationBuilder.DropColumn(
                name: "RawData",
                table: "RuleTransaction");

            migrationBuilder.DropColumn(
                name: "DisplayName",
                table: "Capteur");

            migrationBuilder.AddColumn<string>(
                name: "BaseTopicMQTT",
                table: "Local",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<Guid>(
                name: "SubscriptionId",
                table: "Facture",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddForeignKey(
                name: "FK_Facture_Subscription_SubscriptionId",
                table: "Facture",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Subscription_Client_ClientId",
                table: "Subscription",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id");
        }
    }
}
