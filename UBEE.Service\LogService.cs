﻿using System.Text.Json;
using UBEE.Contracts.Repository;
using UBEE.Contracts.Repository.Base;
using UBEE.MODELS.Common.Models;
using UBEE.MODELS.DataSource.Models;
using UBEE.Service.Contracts;

namespace UBEE.Service;

public class LogService : BaseService<Log>, ILogService
{
    private readonly IGenericRepository<Transaction> _transactionRepository;
    private readonly IGenericRepository<RuleTransaction> _ruleTransactionRepository;

    public LogService(IRepositoryManager unitOfWork) : base(unitOfWork)
    {
        _transactionRepository = UnitOfWork.GetRepository<Transaction>();
        _ruleTransactionRepository = UnitOfWork.GetRepository<RuleTransaction>();
    }

    /// <summary>
    /// Generates logs and RuleTransactions when a rule is applied, based on the rule's related capteurs.
    /// It now handles a status field in the incoming JSON.
    /// </summary>
    /// <param name="idRule">The ID of the rule being applied</param>
    /// <param name="idLocal">The local ID where the rule applies</param>
    /// <param name="idController">The controller ID where the rule applies</param>
    /// <param name="rawDataJson">The JSON string containing the rule data, which may include a status field.</param>
    /// <returns>The number of logs created</returns>
    public async Task<int> GenerateLogsFromRuleAsync(Guid idRule, Guid idLocal, Guid idController, string rawDataJson)
    {
        if (string.IsNullOrWhiteSpace(rawDataJson))
        {
            throw new ArgumentException("RawData JSON cannot be null or empty.", nameof(rawDataJson));
        }

        try
        {
            string logStatus;
            string ruleDataForTopicExtraction;

            // Step 1: Parse the rawDataJson to determine status and the actual rule data for topic extraction.
            using (var doc = JsonDocument.Parse(rawDataJson))
            {
                var root = doc.RootElement;
                if (root.TryGetProperty("status", out var statusElement) && statusElement.ValueKind == JsonValueKind.String)
                {
                    logStatus = statusElement.GetString() ?? "UNKNOWN";
                    // If status is present, the actual rule data is in a nested 'RawData' property
                    if (root.TryGetProperty("RawData", out var rawDataElement))
                    {
                        ruleDataForTopicExtraction = rawDataElement.GetRawText();
                    }
                    else
                    {
                        // Invalid format if status exists without RawData, but handle gracefully.
                        ruleDataForTopicExtraction = "{}";
                    }
                }
                else
                {
                    logStatus = "SUCCESS"; // Default to SUCCESS if no status field
                    ruleDataForTopicExtraction = rawDataJson;
                }
            }

            // Step 2: Extract topics from the relevant part of the JSON
            var ruleTopics = ExtractTopicsFromRuleJson(ruleDataForTopicExtraction);

            if (!ruleTopics.Any())
            {
                // No topics found in the rule, nothing to process
                return 0;
            }

            // Step 3: Get all transactions that match the criteria and have capteurs with matching topics
            var matchingTransactions = await GetMatchingTransactionsAsync(idLocal, idController, ruleTopics);

            if (!matchingTransactions.Any())
            {
                // No matching transactions found
                return 0;
            }

            // Step 4: Create RuleTransactions and Logs for each matching transaction
            var logsCreated = 0;
            var ruleTransactionsToCreate = new List<RuleTransaction>();
            var logsToCreate = new List<Log>();

            foreach (var transaction in matchingTransactions)
            {
                // Create RuleTransaction
                var ruleTransaction = new RuleTransaction
                {
                    Id = Guid.NewGuid(),
                    RawData = rawDataJson, // Log the original, full JSON
                    IdRule = idRule,
                    IdTransaction = transaction.Id,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System",
                    LastUpdatedAt = DateTime.UtcNow,
                    LastUpdatedBy = "System"
                };
                ruleTransactionsToCreate.Add(ruleTransaction);

                // Create Log entry with the determined status
                var log = new Log
                {
                    Id = Guid.NewGuid(),
                    TransactionId = transaction.Id,
                    ControllerId = idController,
                    LocalId = idLocal,
                    CapteurId = transaction.Capteur.Id,
                    Message = $"Rule applied: {idRule} on capteur topic: {transaction.Capteur?.Topic},status:{logStatus},RawData:{rawDataJson}",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System",
                    LastUpdatedAt = DateTime.UtcNow,
                    LastUpdatedBy = "System"
                };
                logsToCreate.Add(log);
                logsCreated++;
            }

            // Step 5: Save all changes
            if (ruleTransactionsToCreate.Any())
            {
                await _ruleTransactionRepository.InsertRange(ruleTransactionsToCreate);
            }

            if (logsToCreate.Any())
            {
                await Repository.InsertRange(logsToCreate);
            }

            await UnitOfWork.SaveAsync();

            return logsCreated;
        }
        catch (JsonException ex)
        {
            throw new ArgumentException($"Invalid JSON format in rawDataJson: {ex.Message}", nameof(rawDataJson), ex);
        }
        catch (Exception ex)
        {
            // Log the exception if you have a logging mechanism
            throw new InvalidOperationException($"Error generating logs from rule: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Extracts all topics from the rule JSON (both from topic_pattern and actions)
    /// </summary>
    /// <param name="ruleJson">The rule JSON string</param>
    /// <returns>A list of unique topics found in the rule</returns>
    private static List<string> ExtractTopicsFromRuleJson(string ruleJson)
    {
        var topics = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        using var document = JsonDocument.Parse(ruleJson);
        var root = document.RootElement;

        // Extract topics from topic_pattern array
        if (root.TryGetProperty("topic_pattern", out var topicPatternElement) &&
            topicPatternElement.ValueKind == JsonValueKind.Array)
        {
            foreach (var topicElement in topicPatternElement.EnumerateArray())
            {
                if (topicElement.ValueKind == JsonValueKind.String)
                {
                    var topic = topicElement.GetString();
                    if (!string.IsNullOrWhiteSpace(topic))
                    {
                        topics.Add(topic.Trim());
                    }
                }
            }
        }

        // Extract topics from actions array
        if (root.TryGetProperty("actions", out var actionsElement) &&
            actionsElement.ValueKind == JsonValueKind.Array) // <-- FIX IS HERE
        {
            foreach (var actionElement in actionsElement.EnumerateArray())
            {
                if (actionElement.TryGetProperty("topic", out var actionTopicElement) &&
                    actionTopicElement.ValueKind == JsonValueKind.String)
                {
                    var topic = actionTopicElement.GetString();
                    if (!string.IsNullOrWhiteSpace(topic))
                    {
                        // Remove '/set' suffix if present for matching
                        var cleanTopic = topic.Trim();
                        if (cleanTopic.EndsWith("/set", StringComparison.OrdinalIgnoreCase))
                        {
                            cleanTopic = cleanTopic.Substring(0, cleanTopic.Length - 4);
                        }
                        topics.Add(cleanTopic);
                    }
                }
            }
        }

        return topics.ToList();
    }

    /// <summary>
    /// Gets all transactions that match the specified criteria and have capteurs with topics used in the rule
    /// </summary>
    /// <param name="idLocal">The local ID</param>
    /// <param name="idController">The controller ID</param>
    /// <param name="ruleTopics">The list of topics from the rule</param>
    /// <returns>List of matching transactions</returns>
    private async Task<List<Transaction>> GetMatchingTransactionsAsync(Guid idLocal, Guid idController, List<string> ruleTopics)
    {
        // Get transactions with the specified Local and Controller, including Capteur navigation property
        var transactions = await _transactionRepository.Get(
            filter: t => t.IdLocal == idLocal && t.IdController == idController && t.IdCapteur != null,
            includeProperties: "Capteur"
        );

        // Filter transactions where the capteur's topic matches any topic from the rule
        var matchingTransactions = transactions.Where(t =>
            t.Capteur != null &&
            !string.IsNullOrWhiteSpace(t.Capteur.Topic) &&
            ruleTopics.Any(ruleTopic =>
                string.Equals(t.Capteur.Topic.Trim(), ruleTopic.Trim(), StringComparison.OrdinalIgnoreCase)
            )
        ).ToList();

        return matchingTransactions;
    }
}