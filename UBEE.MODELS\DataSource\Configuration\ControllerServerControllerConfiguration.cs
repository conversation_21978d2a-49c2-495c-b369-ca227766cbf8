﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class ControllerServerControllerConfiguration : IEntityTypeConfiguration<ControllerServerController>
{
    public void Configure(EntityTypeBuilder<ControllerServerController> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.ControllerServeur)
        .WithMany(s => s.ControllerServerControllers)
        .HasForeignKey(s => s.IdControllerServeur);

        builder.HasOne(s => s.Controller)
        .WithMany(s => s.ControllerServerControllers)
        .HasForeignKey(s => s.IdController);
    }
}
