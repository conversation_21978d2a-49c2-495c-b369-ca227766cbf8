﻿using Microsoft.EntityFrameworkCore;
using System.Text;
using System.Text.RegularExpressions;
using UBEE.Contracts.Repository;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.DTOs;
using UBEE.MODELS.DataSource.Models;
using UBEE.Shared.GenericListModel.Filtering;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace UBEE.Infrastructure.Repository;


public class RulesRepository : IRulesRepository
{
    private readonly UBEEContext _context;

    public RulesRepository(UBEEContext context)
    {
        _context = context;
    }

    public async Task<Pager<RuleComprehensiveDto>> GetRulesComprehensiveAsync(Lister<RuleComprehensiveDto> lister)
    {
        var pageSize = lister.Pagination?.PageSize ?? 10;
        var currentPage = lister.Pagination?.CurrentPage ?? 1;
        var offset = (currentPage - 1) * pageSize;

        // Build the base query
        var baseQuery = BuildBaseQuery();

        // Apply filtering
        var whereClause = BuildWhereClause(lister.FilterParams);
        if (!string.IsNullOrEmpty(whereClause))
        {
            baseQuery += $" WHERE {whereClause}";
        }

        // Get total count using a simpler approach - create a specific DTO for count
        var countQuery = $@"
        SELECT COUNT(*) as CountValue
        FROM ({baseQuery}) AS CountQuery";

        // Use a simple DTO for the count result
        var countResult = await _context.Database.SqlQueryRaw<CountDto>(countQuery).FirstAsync();
        var totalCount = countResult.CountValue;

        // Apply sorting
        var orderBy = BuildOrderByClause(lister.SortParams);

        // Apply pagination
        var pagedQuery = $@"
        {baseQuery}
        {orderBy}
        OFFSET {offset} ROWS
        FETCH NEXT {pageSize} ROWS ONLY";

        var result = await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(pagedQuery).ToListAsync();

        // Create pagination info
        var pagination = new Pagination
        {
            CurrentPage = currentPage,
            PageSize = pageSize,
            TotalElement = totalCount,
            PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
            IsFirst = currentPage == 1,
            IsLast = currentPage >= Math.Ceiling(totalCount / (double)pageSize),
            StartIndex = offset + 1
        };

        return new Pager<RuleComprehensiveDto>
        {
            Content = result,
            Lister = new Lister<RuleComprehensiveDto>
            {
                Pagination = pagination,
                SortParams = lister.SortParams,
                FilterParams = lister.FilterParams
            }
        };
    }

   public async Task<Pager<RuleComprehensiveDto>> SearchRulesAsync(RulesLister rulesLister)
{
    var searchTerm = rulesLister.SearchTerm?.Trim();
    var pageSize = rulesLister.Pagination?.PageSize ?? 10;
    var currentPage = rulesLister.Pagination?.CurrentPage ?? 1;
    var offset = (currentPage - 1) * pageSize;

    // If no search term, return all rules
    if (string.IsNullOrEmpty(searchTerm))
    {
        return await GetRulesComprehensiveAsync(new Lister<RuleComprehensiveDto>
        {
            Pagination = rulesLister.Pagination,
            SortParams = rulesLister.SortParams,
            FilterParams = rulesLister.FilterParams
        });
    }

    // Build search query with direct string replacement (safer approach)
    var escapedSearchTerm = EscapeSqlValue(searchTerm);
    var searchPattern = $"%{escapedSearchTerm}%";
    
    var baseQuery = $@"
        SELECT DISTINCT 
            r.RuleId, 
            r.Enabled, 
            r.RuleSummary,
            r.Priority, 
            r.RawData, 
            r.RuleCreatedAt, 
            r.RuleLastUpdatedAt,
            r.TotalClients, 
            r.TotalSites, 
            r.TotalLocals, 
            r.LastTriggered,
            r.Status,
            '' as TagsString
        FROM vw_RulesComprehensive r
        WHERE (
            LOWER(r.RawData) LIKE LOWER('{searchPattern}') 
            OR LOWER(r.RuleSummary) LIKE LOWER('{searchPattern}')
        )";

    // Add status filter
    if (!rulesLister.IncludeInactive)
    {
        baseQuery += " AND r.Status = 'active'";
    }

    // Apply additional filtering
    var whereClause = BuildWhereClause(rulesLister.FilterParams);
    if (!string.IsNullOrEmpty(whereClause))
    {
        baseQuery += $" AND ({whereClause})";
    }

    // Get total count
    var countQuery = $@"
        SELECT COUNT(*) as CountValue
        FROM ({baseQuery}) AS CountQuery";

    var countResult = await _context.Database.SqlQueryRaw<CountDto>(countQuery).FirstAsync();
    var totalCount = countResult.CountValue;

    // Apply sorting
    var orderBy = BuildOrderByClause(rulesLister.SortParams);

    // Apply pagination
    var pagedQuery = $@"
        {baseQuery}
        {orderBy}
        OFFSET {offset} ROWS
        FETCH NEXT {pageSize} ROWS ONLY";

    var result = await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(pagedQuery).ToListAsync();

    // Create pagination info
    var pagination = new Pagination
    {
        CurrentPage = currentPage,
        PageSize = pageSize,
        TotalElement = totalCount,
        PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
        IsFirst = currentPage == 1,
        IsLast = currentPage >= Math.Ceiling(totalCount / (double)pageSize),
        StartIndex = offset + 1
    };

    return new Pager<RuleComprehensiveDto>
    {
        Content = result,
        Lister = new RulesLister
        {
            Pagination = pagination,
            SortParams = rulesLister.SortParams,
            FilterParams = rulesLister.FilterParams,
            SearchTerm = searchTerm,
            IncludeInactive = rulesLister.IncludeInactive
        }
    };
}

    public async Task<RuleComprehensiveDto?> GetRuleComprehensiveByIdAsync(Guid ruleId)
    {
        var query = $@"
            {BuildBaseQuery()}
            WHERE r.RuleId = '{ruleId}'";

        var result = await _context.Database.SqlQueryRaw<RuleComprehensiveDto>(query).FirstOrDefaultAsync();
        return result;
    }

    public async Task<IEnumerable<RuleClientHierarchyDto>> GetRuleClientHierarchyAsync(Guid ruleId)
    {
        var query = $@"
            SELECT * FROM vw_RulesClientHierarchy
            WHERE RuleId = '{ruleId}'
            ORDER BY ClientId, SiteId, LocalId";

        var result = await _context.Database.SqlQueryRaw<RuleClientHierarchyDto>(query).ToListAsync();
        return result;
    }

    public async Task<IEnumerable<RuleWithTagDto>> GetRuleTagsAsync(Guid ruleId)
    {
        var query = $@"
            SELECT RuleId, Enabled, Priority, RawData, CreatedAt, LastUpdatedAt,
                   TagId, TagCreatedAt, TagName, TagIsActive
            FROM vw_RulesWithTags
            WHERE RuleId = '{ruleId}'";

        var result = await _context.Database.SqlQueryRaw<RuleWithTagDto>(query).ToListAsync();
        return result;
    }


    public async Task<IEnumerable<RuleTransactionDetailDto>> GetRuleTransactionDetailsAsync(Guid ruleId)
    {
        var query = $@"
            SELECT DISTINCT 
                rd.RuleId, 
                rd.RuleEnabled,
                rd.TransactionId, 
                rd.ControllerInControl,
                rd.ControllerId, 
                rd.ControllerIdController, 
                rd.ControllerLocalId,
                rd.TransactionCreatedAt, 
                rd.TransactionCreatedBy,
                rd.TransactionLastUpdatedAt, 
                rd.TransactionLastUpdatedBy,
                rd.RuleTransactionId, 
                rd.LocalName, 
                rd.SiteName, 
                rd.SiteAddress
            FROM vw_RulesTransactionDetails rd
            WHERE rd.RuleId = '{ruleId}'";

        var result = await _context.Database.SqlQueryRaw<RuleTransactionDetailDto>(query).ToListAsync();
        return result;
    }

    #region Private Helper Methods

    private string BuildBaseQuery()
    {
        // Since TagsString is empty, let's use a simpler approach
        return @"
        SELECT 
            r.RuleId, 
            r.Enabled, 
            r.RuleSummary,
            r.Priority, 
            r.RawData, 
            r.RuleCreatedAt, 
            r.RuleLastUpdatedAt,
            r.TotalClients, 
            r.TotalSites, 
            r.TotalLocals, 
            r.LastTriggered,
            r.Status,
            '' as TagsString
        FROM vw_RulesComprehensive r";
    }


    // Add the EscapeSqlValue helper method
    private string EscapeSqlValue(string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        // Basic SQL injection prevention - replace single quotes
        return value.Replace("'", "''");
    }

    private string BuildWhereClause(List<Filtering.WhereParams>? filterParams)
    {
        if (filterParams == null || !filterParams.Any())
            return string.Empty;

        var conditions = new List<string>();

        for (int i = 0; i < filterParams.Count; i++)
        {
            var filter = filterParams[i];

            if (string.IsNullOrEmpty(filter.Column) || filter.Value == null)
                continue;

            var column = GetColumnName(filter.Column);
            var value = EscapeSqlValue(filter.Value.ToString());

            // Build the condition based on operator
            var condition = filter.Op?.ToLower() switch
            {
                "eq" => IsNumericColumn(column) ? $"{column} = {value}" : $"{column} = '{value}'",
                "neq" => IsNumericColumn(column) ? $"{column} != {value}" : $"{column} != '{value}'",
                "gt" => IsNumericColumn(column) ? $"{column} > {value}" : $"{column} > '{value}'",
                "gte" => IsNumericColumn(column) ? $"{column} >= {value}" : $"{column} >= '{value}'",
                "lt" => IsNumericColumn(column) ? $"{column} < {value}" : $"{column} < '{value}'",
                "lte" => IsNumericColumn(column) ? $"{column} <= {value}" : $"{column} <= '{value}'",
                "contains" => $"{column} LIKE '%{value}%'",
                _ => IsNumericColumn(column) ? $"{column} = {value}" : $"{column} = '{value}'"
            };

            // For the first condition, just add it
            if (i == 0)
            {
                conditions.Add(condition);
            }
            else
            {
                // For subsequent conditions, prepend with AND/OR based on AndOr property
                var connector = filter.AndOr?.ToUpper() == "OR" ? " OR " : " AND ";
                conditions.Add($"{connector}{condition}");
            }
        }

        return conditions.Any() ? string.Join("", conditions) : string.Empty;
    }
    private bool IsNumericColumn(string column)
    {
        var numericColumns = new HashSet<string>
    {
        "r.Priority",
        "r.TotalClients",
        "r.TotalSites",
        "r.TotalLocals",
    };

        return numericColumns.Contains(column);
    }

    private string BuildOrderByClause(List<Sorting>? sortParams)
    {
        if (sortParams == null || !sortParams.Any())
        {
            return "ORDER BY r.Priority, r.RuleCreatedAt DESC";
        }

        var orderClauses = new List<string>();

        foreach (var sort in sortParams)
        {
            if (string.IsNullOrEmpty(sort.Column))
                continue;

            var column = GetColumnName(sort.Column);
            var direction = sort.Sort?.ToUpper() == "DESC" ? "DESC" : "ASC";
            orderClauses.Add($"{column} {direction}");
        }

        return orderClauses.Any()
            ? $"ORDER BY {string.Join(", ", orderClauses)}"
            : "ORDER BY r.Priority, r.RuleCreatedAt DESC";
    }

    private string GetColumnName(string column)
    {
        return column?.ToLower() switch
        {
            "priority" => "r.Priority",
            "rulecreatedat" or "createdat" => "r.RuleCreatedAt",
            "rulelastupdatedat" or "lastupdated" => "r.RuleLastUpdatedAt",
            "status" => "r.Status",
            "lasttriggered" => "r.LastTriggered",
            "enabled" => "r.Enabled",
            "totalclients" => "r.TotalClients",
            "totalsites" => "r.TotalSites",
            "totallocals" => "r.TotalLocals",
            _ => "r.Priority" // Default fallback
        };
    }



    // Add these methods to your RulesRepository class



    public async Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionChartDataAsync(Guid ruleId, int days = 30)
    {
        var escapedRuleId = EscapeSqlValue(ruleId.ToString());
        var query = $@"
        SELECT 
            CAST(l.CreatedAt AS DATE) as ExecutionDate,
            SUM(CASE 
                WHEN CHARINDEX('status:SUCCESS', l.Message) > 0 THEN 1 
                ELSE 0 
            END) as SuccessfulExecutions,
            SUM(CASE 
                WHEN CHARINDEX('status:FAILURE', l.Message) > 0 THEN 1 
                ELSE 0 
            END) as FailedExecutions,
            COUNT(*) as TotalExecutions
        FROM Log l
        WHERE l.Message LIKE '%Rule applied: {escapedRuleId}%'
        AND l.Message LIKE '%RawData:%'
        AND CHARINDEX('status:', l.Message) > 0  -- Only count entries with status information
        AND l.CreatedAt >= DATEADD(DAY, -{days}, GETDATE())
        GROUP BY CAST(l.CreatedAt AS DATE)
        ORDER BY ExecutionDate DESC";

        var result = await _context.Database.SqlQueryRaw<RuleExecutionChartDto>(query).ToListAsync();
        return result;
    }

    // Optional: Get hourly data for detailed view
    public async Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionHourlyDataAsync(Guid ruleId, DateTime date)
    {
        var escapedRuleId = EscapeSqlValue(ruleId.ToString());
        var dateString = date.ToString("yyyy-MM-dd");

        var query = $@"
        SELECT 
            DATEADD(HOUR, DATEPART(HOUR, l.CreatedAt), CAST('{dateString}' AS DATETIME)) as ExecutionDate,
            SUM(CASE WHEN l.Message LIKE '%SUCCESS%' THEN 1 ELSE 0 END) as SuccessfulExecutions,
            SUM(CASE WHEN l.Message LIKE '%SUCCESS%' THEN 0 ELSE 1 END) as FailedExecutions,
            COUNT(*) as TotalExecutions
        FROM Log l
        WHERE l.Message LIKE '%Rule applied: {escapedRuleId}%'
        AND l.Message LIKE '%RawData:%'
        AND CAST(l.CreatedAt AS DATE) = '{dateString}'
        GROUP BY DATEPART(HOUR, l.CreatedAt)
        ORDER BY ExecutionDate";

        var result = await _context.Database.SqlQueryRaw<RuleExecutionChartDto>(query).ToListAsync();
        return result;
    }

    public async Task<Pager<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, Lister<RuleExecutionSimpleDto> lister)
    {
        var pageSize = lister.Pagination?.PageSize ?? 20;
        var currentPage = lister.Pagination?.CurrentPage ?? 1;
        var offset = (currentPage - 1) * pageSize;

        var escapedRuleId = EscapeSqlValue(ruleId.ToString());

        // Base query for executions
        var baseQuery = $@"
        SELECT 
            l.CreatedAt as ExecutionTimestamp,
            CASE 
                WHEN l.Message LIKE '%SUCCESS%' THEN CAST(1 AS BIT)
                ELSE CAST(0 AS BIT)
            END as IsSuccess,
            l.ControllerId
        FROM Log l
        WHERE l.Message LIKE '%Rule applied: {escapedRuleId}%'
        AND l.Message LIKE '%RawData:%'";

        // Apply additional filtering if provided
        var whereClause = BuildExecutionWhereClause(lister.FilterParams);
        if (!string.IsNullOrEmpty(whereClause))
        {
            baseQuery += $" AND ({whereClause})";
        }

        // Get total count
        var countQuery = $@"
        SELECT COUNT(*) as CountValue
        FROM ({baseQuery}) AS CountQuery";

        var countResult = await _context.Database.SqlQueryRaw<CountDto>(countQuery).FirstAsync();
        var totalCount = countResult.CountValue;

        // Apply sorting (default to most recent first)
        var orderBy = BuildExecutionOrderByClause(lister.SortParams);

        // Apply pagination
        var pagedQuery = $@"
        {baseQuery}
        {orderBy}
        OFFSET {offset} ROWS
        FETCH NEXT {pageSize} ROWS ONLY";

        var result = await _context.Database.SqlQueryRaw<RuleExecutionSimpleDto>(pagedQuery).ToListAsync();

        // Create pagination info
        var pagination = new Pagination
        {
            CurrentPage = currentPage,
            PageSize = pageSize,
            TotalElement = totalCount,
            PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
            IsFirst = currentPage == 1,
            IsLast = currentPage >= Math.Ceiling(totalCount / (double)pageSize),
            StartIndex = offset + 1
        };

        return new Pager<RuleExecutionSimpleDto>
        {
            Content = result,
            Lister = new Lister<RuleExecutionSimpleDto>
            {
                Pagination = pagination,
                SortParams = lister.SortParams,
                FilterParams = lister.FilterParams
            }
        };
    }
    // Keep one simple method for recent individual executions (for debugging)
    public async Task<IEnumerable<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, int limit = 50)
    {
        var escapedRuleId = EscapeSqlValue(ruleId.ToString());

        var query = $@"
        SELECT TOP {limit}
            l.CreatedAt as ExecutionTimestamp,
            CASE 
                WHEN l.Message LIKE '%SUCCESS%' THEN CAST(1 AS BIT)
                ELSE CAST(0 AS BIT)
            END as IsSuccess,
            l.ControllerId
        FROM Log l
        WHERE l.Message LIKE '%Rule applied: {escapedRuleId}%'
        AND l.Message LIKE '%RawData:%'
        ORDER BY l.CreatedAt DESC";

        var result = await _context.Database.SqlQueryRaw<RuleExecutionSimpleDto>(query).ToListAsync();
        return result;
    }
    private string BuildExecutionWhereClause(List<Filtering.WhereParams>? filterParams)
    {
        if (filterParams == null || !filterParams.Any())
            return string.Empty;

        var conditions = new List<string>();

        for (int i = 0; i < filterParams.Count; i++)
        {
            var filter = filterParams[i];

            if (string.IsNullOrEmpty(filter.Column) || filter.Value == null)
                continue;

            var column = GetExecutionColumnName(filter.Column);
            var value = EscapeSqlValue(filter.Value.ToString());

            var condition = filter.Op?.ToLower() switch
            {
                "eq" => column == "IsSuccess" ? $"{column} = {(value.ToLower() == "true" ? "1" : "0")}" : $"{column} = '{value}'",
                "neq" => column == "IsSuccess" ? $"{column} != {(value.ToLower() == "true" ? "1" : "0")}" : $"{column} != '{value}'",
                "gt" => $"{column} > '{value}'",
                "gte" => $"{column} >= '{value}'",
                "lt" => $"{column} < '{value}'",
                "lte" => $"{column} <= '{value}'",
                "contains" => $"{column} LIKE '%{value}%'",
                _ => column == "IsSuccess" ? $"{column} = {(value.ToLower() == "true" ? "1" : "0")}" : $"{column} = '{value}'"
            };

            if (i == 0)
            {
                conditions.Add(condition);
            }
            else
            {
                var connector = filter.AndOr?.ToUpper() == "OR" ? " OR " : " AND ";
                conditions.Add($"{connector}{condition}");
            }
        }

        return conditions.Any() ? string.Join("", conditions) : string.Empty;
    }

    private string BuildExecutionOrderByClause(List<Sorting>? sortParams)
    {
        if (sortParams == null || !sortParams.Any())
        {
            return "ORDER BY l.CreatedAt DESC"; // Default: most recent first
        }

        var orderClauses = new List<string>();

        foreach (var sort in sortParams)
        {
            if (string.IsNullOrEmpty(sort.Column))
                continue;

            var column = GetExecutionColumnName(sort.Column);
            var direction = sort.Sort?.ToUpper() == "ASC" ? "ASC" : "DESC";
            orderClauses.Add($"{column} {direction}");
        }

        return orderClauses.Any()
            ? $"ORDER BY {string.Join(", ", orderClauses)}"
            : "ORDER BY l.CreatedAt DESC";
    }

    private string GetExecutionColumnName(string column)
    {
        return column?.ToLower() switch
        {
            "executiontimestamp" or "timestamp" or "createdat" => "l.CreatedAt",
            "issuccess" or "success" or "status" => "IsSuccess",
            "controllerid" or "controller" => "l.ControllerId",
            _ => "l.CreatedAt" // Default fallback
        };
    }
    #endregion
}
