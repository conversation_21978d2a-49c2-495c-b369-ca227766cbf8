﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/organisation")]
[ApiController]
public class OrganisationController : BaseController<Organisation>
{
    private readonly IOrganisationService _service;
    public OrganisationController(IOrganisationService baseService) : base(baseService)
    {
        this._service = baseService as IOrganisationService;
        this.UnwantedProperties += ",LogoOrganisation";
        IncludesProperties = "";
    }
}

