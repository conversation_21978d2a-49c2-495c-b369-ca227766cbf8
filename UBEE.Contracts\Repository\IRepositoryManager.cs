﻿using UBEE.Contracts.Repository.Base;
using UBEE.MODELS.Common.Models;
using UBEE.MODELS.DataSource;

namespace UBEE.Contracts.Repository;


public interface IRepositoryManager : IDisposable
{
    #region Properties
    /// <summary>
    /// context
    /// </summary>
    /// <value>
    /// The context.
    /// </value>
    UBEEContext Context { get; }

    #endregion

    #region Methods

    IGenericRepository<TEntity> GetRepository<TEntity>() where TEntity : BaseClass;

    /// <summary>
    /// Saves this instance.
    /// </summary>
    int Save();
    Task<int> SaveAsync();
    #endregion
}
