﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/licence-option")]
[ApiController]
public class LicenceOptionController : BaseController<LicenceOption>
{
    private readonly ILicenceOptionService _service;
    public LicenceOptionController(ILicenceOptionService baseService) : base(baseService)
    {
        this._service = baseService as ILicenceOptionService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}

