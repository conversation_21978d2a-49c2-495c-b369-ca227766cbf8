﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class changeclientmodel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "BaseTopic",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "Surface",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "IsEnabled",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "LogoClient",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreEquipementsActif",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreEquipementsInactif",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreLocaux",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "Nom",
                table: "Local",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "Local",
                newName: "BaseTopicMQTT");

            migrationBuilder.RenameColumn(
                name: "Telephone",
                table: "Client",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "PasswordConfirmed",
                table: "Client",
                newName: "SIRET");

            migrationBuilder.RenameColumn(
                name: "Password",
                table: "Client",
                newName: "SIREN");

            migrationBuilder.RenameColumn(
                name: "NomComplet",
                table: "Client",
                newName: "Region");

            migrationBuilder.RenameColumn(
                name: "Email",
                table: "Client",
                newName: "RC");

            migrationBuilder.AddColumn<int>(
                name: "ActiveEquipment",
                table: "Client",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Brand",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "BusinessSector",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<Guid>(
                name: "ClientId",
                table: "Client",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClientStatus",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateOnly>(
                name: "CompanyCreationDate",
                table: "Client",
                type: "date",
                nullable: false,
                defaultValue: new DateOnly(1, 1, 1));

            migrationBuilder.AddColumn<string>(
                name: "CompanySize",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CompanyType",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContactAddress",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContactEmail",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContactName",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Filiale",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ICE",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "IF",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "InactiveEquipment",
                table: "Client",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LegalForm",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Patente",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_Client_ClientId",
                table: "Client",
                column: "ClientId");

            migrationBuilder.AddForeignKey(
                name: "FK_Client_Client_ClientId",
                table: "Client",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Client_Client_ClientId",
                table: "Client");

            migrationBuilder.DropForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence");

            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site");

            migrationBuilder.DropIndex(
                name: "IX_Client_ClientId",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ActiveEquipment",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Brand",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "BusinessSector",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "City",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ClientId",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ClientStatus",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanyCreationDate",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanySize",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "CompanyType",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactAddress",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactEmail",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ContactName",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Filiale",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "ICE",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "IF",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "InactiveEquipment",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "LegalForm",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Patente",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "Local",
                newName: "Nom");

            migrationBuilder.RenameColumn(
                name: "BaseTopicMQTT",
                table: "Local",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "Client",
                newName: "Telephone");

            migrationBuilder.RenameColumn(
                name: "SIRET",
                table: "Client",
                newName: "PasswordConfirmed");

            migrationBuilder.RenameColumn(
                name: "SIREN",
                table: "Client",
                newName: "Password");

            migrationBuilder.RenameColumn(
                name: "Region",
                table: "Client",
                newName: "NomComplet");

            migrationBuilder.RenameColumn(
                name: "RC",
                table: "Client",
                newName: "Email");

            migrationBuilder.AddColumn<string>(
                name: "BaseTopic",
                table: "Local",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<double>(
                name: "Surface",
                table: "Local",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "IpAdresse",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsEnabled",
                table: "Client",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<byte[]>(
                name: "LogoClient",
                table: "Client",
                type: "varbinary(max)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<int>(
                name: "NombreEquipementsActif",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NombreEquipementsInactif",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NombreLocaux",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddForeignKey(
                name: "FK_Licence_Client_IdClient",
                table: "Licence",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
