﻿using UBEE.MODELS.Common.Models;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Service.Contracts;
public interface IBaseService<TEntity> where TEntity : BaseClass
{
    Task<IEnumerable<TEntity>> AddRange(List<TEntity> entities, string includeProperties = "");
    Task<IEnumerable<TEntity>> GetAll();
    Task<IEnumerable<TEntity>> Get(System.Linq.Expressions.Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, string includeProperties = "", int skip = 0, int take = 0);
    Task<TEntity> GetById(Guid id);
    Task<TEntity> GetOne(Guid id, string includeProperties = "");
    Task<TEntity> GetByChildId(Guid id, string includeProperties = "", string entityName = "");
    Task<TEntity> GetFirstOrDefault();
    TEntity CheckIfExistByProperty(string propertyName, object value);
    Task<IEnumerable<TEntity>> GetAllByChildId(Guid id, string entityName, string orderBy = "", bool orderDesc = false, string includeProperties = "");
    Task<int> Count();
    Task<TEntity> Add(TEntity entity, string includeProperties = "");
    Task<TEntity> Update(TEntity entity, string propertiesToCheck = "");
    TEntity UpdateValues(TEntity entity);
    TEntity Delete(TEntity entity);
    Task<bool> Delete(Guid id);
    Task<Pager<TEntity>> GetPages(Lister<TEntity> lister, string includeProperties = "", IQueryable<TEntity> query = null);

    /// <summary>
    /// Generate a Reference for entities
    /// </summary>
    /// <returns></returns>
    string GenerateReference();
}
