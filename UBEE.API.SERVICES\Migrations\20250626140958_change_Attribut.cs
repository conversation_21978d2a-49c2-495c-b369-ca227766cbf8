﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class change_Attribut : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Readable",
                table: "Variables");

            migrationBuilder.RenameColumn(
                name: "Nom",
                table: "Variables",
                newName: "Key");

            migrationBuilder.RenameColumn(
                name: "Capabilities",
                table: "TypeCapteur",
                newName: "Topic");

            migrationBuilder.AlterColumn<string>(
                name: "Actions",
                table: "Variables",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<string>(
                name: "DeviceType",
                table: "TypeCapteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DisplayName",
                table: "TypeCapteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeviceType",
                table: "TypeCapteur");

            migrationBuilder.DropColumn(
                name: "DisplayName",
                table: "TypeCapteur");

            migrationBuilder.RenameColumn(
                name: "Key",
                table: "Variables",
                newName: "Nom");

            migrationBuilder.RenameColumn(
                name: "Topic",
                table: "TypeCapteur",
                newName: "Capabilities");

            migrationBuilder.AlterColumn<string>(
                name: "Actions",
                table: "Variables",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Readable",
                table: "Variables",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
