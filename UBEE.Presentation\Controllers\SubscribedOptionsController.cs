﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/subscribed-options")]
[ApiController]
public class SubscribedOptionsController : BaseController<SubscribedOptions>
{
    private readonly ISubscribedOptionsService _service;
    public SubscribedOptionsController(ISubscribedOptionsService baseService) : base(baseService)
    {
        this._service = baseService as ISubscribedOptionsService;
        this.UnwantedProperties += "";
        IncludesProperties = "Option,Subscription";
    }
}

