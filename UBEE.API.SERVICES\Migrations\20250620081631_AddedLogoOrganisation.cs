﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class AddedLogoOrganisation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Client_Organisation_IdOrganisation",
                table: "Client");

            migrationBuilder.RenameColumn(
                name: "LogoOrganisation",
                table: "Client",
                newName: "LogoClient");

            migrationBuilder.AddColumn<byte[]>(
                name: "LogoOrganisation",
                table: "Organisation",
                type: "varbinary(max)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddForeignKey(
                name: "FK_Client_Organisation_IdOrganisation",
                table: "Client",
                column: "IdOrganisation",
                principalTable: "Organisation",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Client_Organisation_IdOrganisation",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "LogoOrganisation",
                table: "Organisation");

            migrationBuilder.RenameColumn(
                name: "LogoClient",
                table: "Client",
                newName: "LogoOrganisation");

            migrationBuilder.AddForeignKey(
                name: "FK_Client_Organisation_IdOrganisation",
                table: "Client",
                column: "IdOrganisation",
                principalTable: "Organisation",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
