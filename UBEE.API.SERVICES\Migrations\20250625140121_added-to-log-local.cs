﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedtologlocal : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Log_Transaction_IdTransaction",
                table: "Log");

            migrationBuilder.RenameColumn(
                name: "IdTransaction",
                table: "Log",
                newName: "TransactionId");

            migrationBuilder.RenameColumn(
                name: "IdLocal",
                table: "Log",
                newName: "LocalId");

            migrationBuilder.RenameIndex(
                name: "IX_Log_IdTransaction",
                table: "Log",
                newName: "IX_Log_TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_Log_LocalId",
                table: "Log",
                column: "LocalId");

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Local_LocalId",
                table: "Log",
                column: "LocalId",
                principalTable: "Local",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Transaction_TransactionId",
                table: "Log",
                column: "TransactionId",
                principalTable: "Transaction",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Log_Local_LocalId",
                table: "Log");

            migrationBuilder.DropForeignKey(
                name: "FK_Log_Transaction_TransactionId",
                table: "Log");

            migrationBuilder.DropIndex(
                name: "IX_Log_LocalId",
                table: "Log");

            migrationBuilder.RenameColumn(
                name: "TransactionId",
                table: "Log",
                newName: "IdTransaction");

            migrationBuilder.RenameColumn(
                name: "LocalId",
                table: "Log",
                newName: "IdLocal");

            migrationBuilder.RenameIndex(
                name: "IX_Log_TransactionId",
                table: "Log",
                newName: "IX_Log_IdTransaction");

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Transaction_IdTransaction",
                table: "Log",
                column: "IdTransaction",
                principalTable: "Transaction",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
