﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="wwwroot\logoIOT.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="QuestPDF" Version="2025.7.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UBEE.Contracts\UBEE.Contracts.csproj" />
    <ProjectReference Include="..\UBEE.LoggerService\UBEE.LoggerService.csproj" />
    <ProjectReference Include="..\UBEE.MODELS\UBEE.Models.csproj" />
    <ProjectReference Include="..\UBEE.Presentation\UBEE.Presentation.csproj" />
    <ProjectReference Include="..\UBEE.Repository\UBEE.Repository.csproj" />
    <ProjectReference Include="..\UBEE.Service.Contracts\UBEE.Service.Contracts.csproj" />
    <ProjectReference Include="..\UBEE.Service\UBEE.Service.csproj" />
  </ItemGroup>

	<ItemGroup>
		<Content Include="wwwroot\**\*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

</Project>
