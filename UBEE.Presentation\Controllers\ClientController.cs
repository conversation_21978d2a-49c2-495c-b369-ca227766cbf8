﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Presentation.Models;
using UBEE.Service.Contracts;
using UBEE.Shared.DataTransferObjects.Dto;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Presentation.Controllers;

[Route("api/client")]
[ApiController]
public class ClientController : BaseController<Client>
{
    private readonly IClientService _service;
    private readonly UBEEContext _context;
    private readonly ISiteService _siteService;

    public ClientController(IClientService baseService, UBEEContext context, ISiteService siteService) : base(baseService)
    {
        this._service = baseService as IClientService;
        this.UnwantedProperties += ",ClientLogo";
        IncludesProperties = "Organisation";
        _context = context;
        this._siteService = siteService as ISiteService;
    }

    [HttpPost("client-activation")]
    public async Task<IActionResult> ActiverClient([FromBody] ActivationDto clientId)
    {
        var ClientData = await _service.GetOne(clientId.Id, IncludesProperties);
        ClientData.Status = "Actif";
        ClientData.ClientStatus = "Actif";
        await _service.Update(ClientData, "Organisation");
        return Ok("OK");
    }

    [HttpPost("client-desactivation")]
    public async Task<IActionResult> DesactiverClient([FromBody] ActivationDto clientId)
    {
        var SiteData = await _siteService.Get(site => site.ClientId == clientId.Id);
        if(SiteData.Count() == 0)
        {
            var ClientData = await this._service.GetOne(clientId.Id, IncludesProperties);
            ClientData.Status = "Inactif";
            ClientData.ClientStatus = "Inactif";
            return Ok(await _service.Update(ClientData, "Organisation"));
        }
        return Ok();
    }

    [HttpGet("sitestatus/{idclient}")]
    public async Task<IActionResult> GetClientsWithSiteStatus(Guid idclient)
    {
        var results = await _context.ClientWithSiteStatusView.Where(v => v.ClientId == idclient).ToListAsync();

        return Ok(results);
    }

    [HttpPost("sitestatus")]
    public async Task<IActionResult> SearchVwClientSite(Lister<ClientWithSiteStatus> lister)
    {
        Pager<ClientWithSiteStatus> pager = new Pager<ClientWithSiteStatus>();
        //var results = await _context.ClientWithSiteStatusView.;
        var query = _context.ClientWithSiteStatusView.AsQueryable();
        var idInfo = typeof(ClientWithSiteStatus).GetProperty("ClientId");

        if (lister.SortParams != null && lister.SortParams.Any())
        {
            foreach (var sort in lister.SortParams)
            {
                query = query.OrderByProperty(sort.Column, sort.Sort == "asc");
            }
        }
        else
        {
            // Default sort if nothing provided
            query = query.OrderByProperty("ClientId");
        }

        if (!(lister.FilterParams is null))
        {
            var wheres = new Filtering.WhereParams[lister.FilterParams.Count()];

            for (int i = 0; i < wheres.Length; i++)
            {
                wheres[i] = lister.FilterParams[i];
            }

            query = query.Where(wheres);
        }
        lister.Pagination.TotalElement = query.Count();
        if (lister.Pagination != null)
        {
            var skip = (lister.Pagination.CurrentPage - lister.Pagination.StartIndex) * lister.Pagination.PageSize;
            if (query.Count() <= lister.Pagination.PageSize)
            {
                lister.Pagination.CurrentPage = lister.Pagination.StartIndex;
                skip = 0;
            }
            if (skip > 0)
                query = lister.SortParams == null || !lister.SortParams.Any()
                    ? query.AsEnumerable().OrderBy(x => idInfo?.GetValue(x, null)).AsQueryable().Skip(skip)
                    : query.Skip(skip);
            var take = query.Count() < lister.Pagination.PageSize ? query.Count() : lister.Pagination.PageSize;

            lister.Pagination.PageCount = !query.Any() ? 1 : lister.Pagination.TotalElement % lister.Pagination.PageSize != 0
                ? (lister.Pagination.TotalElement / lister.Pagination.PageSize) + 1
                : (lister.Pagination.TotalElement / lister.Pagination.PageSize);

            if (take > 0)
                query = query.Take(take);

            lister.Pagination.IsLast = lister.Pagination.CurrentPage == lister.Pagination.PageCount;
            lister.Pagination.IsFirst = lister.Pagination.CurrentPage == 1;
        }



        pager.Content = await query.ToListAsync();

        pager.Lister = lister;

        return Ok(pager);
    }

    [HttpGet("user/{userId}")]
    public async Task<IActionResult> GetByUserId(string userId)
    {
        if (string.IsNullOrEmpty(userId))
            return BadRequest("User ID is required");

        var client = await _service.Get(c => c.UserId == userId, includeProperties: IncludesProperties);
        var clientData = client.FirstOrDefault();

        if (clientData == null)
            return NotFound($"No client found for user ID: {userId}");

        return Ok(clientData.Marshel(WantedProperties, UnwantedProperties));
    }
}
