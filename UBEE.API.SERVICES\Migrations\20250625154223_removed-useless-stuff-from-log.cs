﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class removeduselessstufffromlog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Log_Capteur_CapteurId",
                table: "Log");

            migrationBuilder.DropForeignKey(
                name: "FK_Log_Controller_ControllerId",
                table: "Log");

            migrationBuilder.DropForeignKey(
                name: "FK_Log_Local_LocalId",
                table: "Log");

            migrationBuilder.DropIndex(
                name: "IX_Log_CapteurId",
                table: "Log");

            migrationBuilder.DropIndex(
                name: "IX_Log_ControllerId",
                table: "Log");

            migrationBuilder.DropIndex(
                name: "IX_Log_LocalId",
                table: "Log");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Log_CapteurId",
                table: "Log",
                column: "CapteurId");

            migrationBuilder.CreateIndex(
                name: "IX_Log_ControllerId",
                table: "Log",
                column: "ControllerId");

            migrationBuilder.CreateIndex(
                name: "IX_Log_LocalId",
                table: "Log",
                column: "LocalId");

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Capteur_CapteurId",
                table: "Log",
                column: "CapteurId",
                principalTable: "Capteur",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Controller_ControllerId",
                table: "Log",
                column: "ControllerId",
                principalTable: "Controller",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Log_Local_LocalId",
                table: "Log",
                column: "LocalId",
                principalTable: "Local",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
