namespace UBEE.Shared.DataTransferObjects.Dto
{
    public class ClientWithSiteStatusDto
    {
        public Guid ClientId { get; set; }
        public string Name { get; set; }
        public string ContactEmail { get; set; }
        public string Phone { get; set; }
        public string Organisation { get; set; }
        public string ContactAddress { get; set; }
        public string RC { get; set; }
        public string IF { get; set; }
        public string ClientStatus { get; set; }
        public string SiteInactif { get; set; }
        public string SiteActif { get; set; }
        public string SiteEnMaintenance { get; set; }
        public string SiteEnInstallation { get; set; }
    }
}