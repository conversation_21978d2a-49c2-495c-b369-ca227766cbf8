﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class madesubidoptional : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ControllerServeur_Subscription_SubscriptionId",
                table: "ControllerServeur");

            migrationBuilder.AlterColumn<Guid>(
                name: "SubscriptionId",
                table: "ControllerServeur",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddForeignKey(
                name: "FK_ControllerServeur_Subscription_SubscriptionId",
                table: "ControllerServeur",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropFore<PERSON><PERSON>ey(
                name: "FK_ControllerServeur_Subscription_SubscriptionId",
                table: "ControllerServeur");

            migrationBuilder.AlterColumn<Guid>(
                name: "SubscriptionId",
                table: "ControllerServeur",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ControllerServeur_Subscription_SubscriptionId",
                table: "ControllerServeur",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
