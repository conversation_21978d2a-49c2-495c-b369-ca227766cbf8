﻿using UBEE.Contracts.Repository.Repositories;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Repository.Repository.Base;

namespace UBEE.Repository.Repository.Implementation;

public class ControllerServeurRepository : GenericRepository<ControllerServeur>, IControllerServeurRepository
{
    public ControllerServeurRepository(UBEEContext context) : base(context)
    {
    }
}
