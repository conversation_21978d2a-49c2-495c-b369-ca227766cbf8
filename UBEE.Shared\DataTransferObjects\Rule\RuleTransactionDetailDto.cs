﻿// Updated RuleTransactionDetailDto to match your actual data structure
namespace UBEE.MODELS.DataSource.DTOs
{
    public class RuleTransactionDetailDto
    {
        public Guid RuleId { get; set; }
        public bool? RuleEnabled { get; set; }

        // Transaction details
        public Guid? TransactionId { get; set; }
        public bool? ControllerInControl { get; set; }
        public Guid? ControllerId { get; set; } // Nullable
        public Guid? ControllerIdController { get; set; } // Nullable - This is the actual controller ID
        public Guid? ControllerLocalId { get; set; }
        public DateTime? TransactionCreatedAt { get; set; }
        public string? TransactionCreatedBy { get; set; }
        public DateTime? TransactionLastUpdatedAt { get; set; } // Nullable
        public string? TransactionLastUpdatedBy { get; set; } // Nullable

        // Rule transaction details
        public Guid? RuleTransactionId { get; set; }

        // Contextual information
        public string? LocalName { get; set; }
        public string? SiteName { get; set; }
        public string? SiteAddress { get; set; }

    }
}