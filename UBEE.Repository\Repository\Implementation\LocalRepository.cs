﻿using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Reflection;
using UBEE.Contracts.Repository.Repositories;
using UBEE.MODELS.Common.Models;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Repository.Repository.Base;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Repository.Repository.Implementation;

public class LocalRepository : GenericRepository<Local>, ILocalRepository
{
    public LocalRepository(UBEEContext context) : base(context)
    {
    }

    public async Task<Pager<Local>> GetPages(Lister<Local> lister, string includeProperties = "", IQueryable<Local> query = null)
    {
        Pager<Local> pager = new Pager<Local>();

        if (query == null)
        {
            query = DbSet;
        }

        var idInfo = typeof(Local).GetProperty("Id");
        
        query = includeProperties.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Aggregate(query, (current, includeProperty) => current.Include(includeProperty));

        if (!(lister.SortParams is null))
        {
            IOrderedEnumerable<Local> orderedList = null;

            using (var e = lister.SortParams.GetEnumerator())
            {
                if (e.MoveNext() && e.Current?.Column != null)
                {
                    var paramInfo = typeof(Local).GetProperty(e.Current.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (paramInfo != null && typeof(BaseClass).IsAssignableFrom(paramInfo.PropertyType?.BaseType))
                    {
                        paramInfo = paramInfo.GetType().GetProperty("Libelle") == null
                            ? typeof(Local).GetProperty("Id" + paramInfo.Name,
                                BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance)
                            : typeof(Local).GetProperty(paramInfo.Name + ".Libelle",
                                BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    }
                    switch (e.Current.Sort)
                    {
                        case "asc":
                            orderedList = orderedList == null
                                ? query.AsEnumerable().OrderBy(x => paramInfo?.GetValue(x, null))
                                : orderedList.ThenBy(x =>
                                    paramInfo?.GetValue(x, null));
                            break;
                        case "desc":
                            orderedList = orderedList == null
                                ? query.AsEnumerable().OrderByDescending(x => paramInfo?.GetValue(x, null))
                                : orderedList.ThenByDescending(x =>
                                    paramInfo?.GetValue(x, null));
                            break;
                        default: throw new ArgumentException("Bad ordering operator.");
                    }
                }

                if (orderedList != null) query = orderedList.AsQueryable();
            }

        }

        if (!(lister.FilterParams is null))
        {
            var wheres = new Filtering.WhereParams[lister.FilterParams.Count()];

            for (int i = 0; i < wheres.Length; i++)
            {
                wheres[i] = lister.FilterParams[i];
            }

            query = query.Where(wheres);
        }
        lister.Pagination.TotalElement = query.Count();
        if (lister.Pagination != null)
        {
            var skip = (lister.Pagination.CurrentPage - lister.Pagination.StartIndex) * lister.Pagination.PageSize;
            if (query.Count() <= lister.Pagination.PageSize)
            {
                lister.Pagination.CurrentPage = lister.Pagination.StartIndex;
                skip = 0;
            }

            if (skip > 0)
                query = lister.SortParams == null || !lister.SortParams.Any() ? query.AsEnumerable().OrderBy(x => idInfo?.GetValue(x, null)).AsQueryable().Skip(skip) :
                    query.Skip(skip);
            var take = query.Count() < lister.Pagination.PageSize ? query.Count() : lister.Pagination.PageSize;

            lister.Pagination.PageCount = !query.Any() ? 1 : lister.Pagination.TotalElement % lister.Pagination.PageSize != 0
                ? (lister.Pagination.TotalElement / lister.Pagination.PageSize) + 1
                : (lister.Pagination.TotalElement / lister.Pagination.PageSize);

            if (take > 0)
                query = query.Take(take);

            lister.Pagination.IsLast = lister.Pagination.CurrentPage == lister.Pagination.PageCount;
            lister.Pagination.IsFirst = lister.Pagination.CurrentPage == 1;
        }



        pager.Content = query.Select(
            s=> new Local
            {
                Capacity = s.Capacity,
                CreatedAt = s.CreatedAt,
                CreatedBy = s.CreatedBy,
                DeletedAt = s.DeletedAt,
                DeletedBy = s.DeletedBy,
                Floor = s.Floor,
                Id = s.Id,
                IdSite = s.IdSite,
                LastUpdatedAt = s.LastUpdatedAt,
                LastUpdatedBy = s.LastUpdatedBy,
                Latitude = s.Latitude,
                Longtitude = s.Longtitude,
                Name = s.Name,
                SensorsCount = s.SensorsCount,
                Site = s.Site,
                TypeLocal = s.TypeLocal,
                TypeLocalId = s.TypeLocalId,
            }
        ).ToList();

        pager.Lister = lister;
        return pager;


    }

    public async override Task<Local> GetOne(object id, string includeProperties)
    {
        IQueryable<Local> query = DbSet;
        if (query.Any())
        {
            query = GetQueryEntity(query, id);
            foreach (var includeProperty in includeProperties.Split
                (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }
        }

        return await query.Select(s => new Local
        {
            Capacity = s.Capacity,
            CreatedAt = s.CreatedAt,
            CreatedBy = s.CreatedBy,
            DeletedAt = s.DeletedAt,
            DeletedBy = s.DeletedBy,
            Floor = s.Floor,
            Id = s.Id,
            IdSite = s.IdSite,
            LastUpdatedAt = s.LastUpdatedAt,
            LastUpdatedBy = s.LastUpdatedBy,
            Latitude = s.Latitude,
            Longtitude = s.Longtitude,
            Name = s.Name,
            SensorsCount = s.SensorsCount,
            Site = s.Site,
            TypeLocal = s.TypeLocal,
            TypeLocalId = s.TypeLocalId,
        }).FirstOrDefaultAsync();
    }

    public async Task<string> GetArchitecture(object id)
    {
        IQueryable<Local> query = DbSet;

        return await query.Where(s=> s.Id == Guid.Parse(id.ToString())).Select(s=> s.Architecture2DImage).FirstOrDefaultAsync();
    }
}
