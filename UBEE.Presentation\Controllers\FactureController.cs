﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/facture")]
[ApiController]
public class FactureController : BaseController<Facture>
{
    private readonly IFactureService _service;
    public FactureController(IFactureService baseService) : base(baseService)
    {
        this._service = baseService as IFactureService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}

