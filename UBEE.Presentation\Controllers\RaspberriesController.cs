﻿using Microsoft.AspNetCore.Mvc;
using Zeroconf;

namespace UBEE.Presentation.Controllers;

[ApiController]
[Route("api/raspberry")]
public class RaspberriesController : ControllerBase
{
    [HttpGet("discover")]
    public async Task<IActionResult> Discover()
    {
        const string serviceType = "_raspi-controller._tcp.local.";
        var raspberryList = new List<RaspberryDevice>();

        try
        {
            // Discover all devices advertising the service
            var responses = await ZeroconfResolver.ResolveAsync(serviceType, TimeSpan.FromSeconds(5));

            foreach (var response in responses)
            {
                var service = response.Services.FirstOrDefault().Value;
                if (service == null) continue;

                // Flatten Properties (Zeroconf returns a list of dictionaries)
                var properties = service.Properties
                    .SelectMany(dict => dict)
                    .ToDictionary(kv => kv.Key, kv => kv.Value);

                // Extract properties
                properties.TryGetValue("base_topic", out var baseTopic);
                properties.TryGetValue("hostname", out var hostname);

                if (string.IsNullOrEmpty(baseTopic)) continue;

                raspberryList.Add(new RaspberryDevice
                {
                    Hostname = hostname ?? response.DisplayName,
                    BaseTopic = baseTopic,
                    IP = response.IPAddresses.FirstOrDefault()?.ToString() ?? "Unknown"
                });
            }

            return Ok(raspberryList);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error during discovery: {ex.Message}");
        }
    }

    public class RaspberryDevice
    {
        public string Hostname { get; set; } = string.Empty;
        public string BaseTopic { get; set; } = string.Empty;
        public string IP { get; set; } = string.Empty;
    }
}