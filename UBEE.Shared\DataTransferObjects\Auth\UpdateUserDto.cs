using System.ComponentModel.DataAnnotations;

namespace UBEE.Shared.DataTransferObjects.Auth;

/// <summary>
/// Data transfer object for updating user information
/// Matches the AspNetUsers table schema
/// </summary>
public class UpdateUserDto
{
    /// <summary>
    /// User's username
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// User's email address
    /// </summary>
    [EmailAddress]
    public string? Email { get; set; }

    /// <summary>
    /// Whether the email is confirmed
    /// </summary>
    public bool? EmailConfirmed { get; set; }

    /// <summary>
    /// New password for the user (minimum 6 characters)
    /// </summary>
    [MinLength(6)]
    public string? NewPassword { get; set; }

    /// <summary>
    /// User's phone number
    /// </summary>
    [Phone]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Whether the phone number is confirmed
    /// </summary>
    public bool? PhoneNumberConfirmed { get; set; }

    /// <summary>
    /// Whether two-factor authentication is enabled
    /// </summary>
    public bool? TwoFactorEnabled { get; set; }

    /// <summary>
    /// Lockout end date and time (UTC)
    /// </summary>
    public DateTimeOffset? LockoutEnd { get; set; }

    /// <summary>
    /// Whether lockout is enabled for this user
    /// </summary>
    public bool? LockoutEnabled { get; set; }

    /// <summary>
    /// Number of failed access attempts
    /// </summary>
    [Range(0, int.MaxValue)]
    public int? AccessFailedCount { get; set; }

    /// <summary>
    /// List of roles to assign to the user
    /// </summary>
    public List<string>? Roles { get; set; }
}