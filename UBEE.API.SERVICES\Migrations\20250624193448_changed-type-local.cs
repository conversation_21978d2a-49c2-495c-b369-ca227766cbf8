﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class changedtypelocal : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TypeLocal_Local_IdLocal",
                table: "TypeLocal");

            migrationBuilder.DropIndex(
                name: "IX_TypeLocal_IdLocal",
                table: "TypeLocal");

            migrationBuilder.DropColumn(
                name: "IdLocal",
                table: "TypeLocal");

            migrationBuilder.AddColumn<Guid>(
                name: "TypeLocalId",
                table: "Local",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Local_TypeLocalId",
                table: "Local",
                column: "TypeLocalId");

            migrationBuilder.AddForeignKey(
                name: "FK_Local_TypeLocal_TypeLocalId",
                table: "Local",
                column: "TypeLocalId",
                principalTable: "TypeLocal",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Local_TypeLocal_TypeLocalId",
                table: "Local");

            migrationBuilder.DropIndex(
                name: "IX_Local_TypeLocalId",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "TypeLocalId",
                table: "Local");

            migrationBuilder.AddColumn<Guid>(
                name: "IdLocal",
                table: "TypeLocal",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_TypeLocal_IdLocal",
                table: "TypeLocal",
                column: "IdLocal");

            migrationBuilder.AddForeignKey(
                name: "FK_TypeLocal_Local_IdLocal",
                table: "TypeLocal",
                column: "IdLocal",
                principalTable: "Local",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
