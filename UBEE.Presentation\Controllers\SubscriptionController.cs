﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/subscription")]
[ApiController]
public class SubscriptionController : BaseController<Subscription>
{
    private readonly ISubscriptionService _service;
    private readonly UBEEContext _context;
    public SubscriptionController(ISubscriptionService baseService, UBEEContext context) : base(baseService)
    {
        this._service = baseService as ISubscriptionService;
        this.UnwantedProperties += "";
        IncludesProperties = "Licence,SubscribedOptions";
        _context = context;
    }

    [HttpPost("renew/{id}")]
    public async Task<IActionResult> Renew(Guid id)
    {
        var subscription = await _context.Subscription
            .Include(s => s.Licence)
                .ThenInclude(l => l.LicenceOptions)
                    .ThenInclude(lo => lo.Option)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (subscription == null)
            return NotFound("Subscription not found.");

        var licence = subscription.Licence;
        if (licence == null)
            return BadRequest("Subscription is missing a licence.");

        // Always monthly
        string paymentFrequency = "monthly";

        // Calculate price from Option.Price
        float totalPrice = 0;

        if (licence.LicenceOptions != null)
        {
            foreach (var licenceOption in licence.LicenceOptions)
            {
                var option = licenceOption.Option;
                if (option == null) continue;

                totalPrice += option.Price.Value; // Always monthly price
            }
        }

        // Set new start/end dates
        DateTime currentEnd = subscription.DateFin ?? DateTime.Now;
        DateTime newEnd;
        if (paymentFrequency== "monthly")
        {
            newEnd = currentEnd.AddMonths(1);
        }
        else
        {
            newEnd = currentEnd.AddYears(1);
        }

        // Update subscription
        subscription.PaymentFrequency = paymentFrequency;
        subscription.DateDebut = currentEnd;
        subscription.DateFin = newEnd;
        subscription.Status = "Active";
        subscription.Price = totalPrice;

        // Create facture
        var facture = new Facture
        {
            Id = Guid.NewGuid(),
            Total = totalPrice,
            Status = "Pending",
            IdLicence = subscription.LicenceId,
            Licence = subscription.Licence,
            SubscriptionId = subscription.Id,
            Subscription = subscription,
            CreatedAt = DateTime.Now
        };

        _context.Factures.Add(facture);
        await _context.SaveChangesAsync();

        return Ok(new
        {
            subscription,
            facture
        });
    }

}