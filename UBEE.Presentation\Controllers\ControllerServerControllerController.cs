﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/controller-serveur-controller")]
[ApiController]
public class ControllerServerControllerController : BaseController<ControllerServerController>
{
    private readonly IControllerServerControllerService _service;
    private readonly UBEEContext _context;
    public ControllerServerControllerController(IControllerServerControllerService baseService, UBEEContext context) : base(baseService)
    {
        this._service = baseService as IControllerServerControllerService;
        this.UnwantedProperties += "";
        IncludesProperties = "Controller,ControllerServeur";
        _context = context;
    }

    [HttpGet("get-all-light")]
    public async Task<ActionResult<IEnumerable<object>>> GetLightControllerServeurs([FromQuery] Guid? idControllerServeur)
    {
        var query = _context.ControllerServerController.AsNoTracking();

        if (idControllerServeur.HasValue)
        {
            query = query.Where(cs => cs.IdControllerServeur == idControllerServeur.Value);
        }

        var servers = await query
            .Select(cs => new
            {
                cs.Id,
                cs.IdControllerServeur,
                cs.IdController
            })
            .ToListAsync();

        return Ok(servers);
    }
}

