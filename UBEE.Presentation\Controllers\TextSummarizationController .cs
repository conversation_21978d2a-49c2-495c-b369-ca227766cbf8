﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using UBEE.Service.Contracts;
using UBEE.Shared.DataTransferObjects.Dto;

namespace UBEE.Presentation.Controllers;

[Route("api/text-summarization")]
[ApiController]
public class TextSummarizationController : ControllerBase
{
    private readonly ITextSummarizationService _summarizationService;
    private readonly ILogger<TextSummarizationController> _logger;

    public TextSummarizationController(
        ITextSummarizationService summarizationService,
        ILogger<TextSummarizationController> logger)
    {
        _summarizationService = summarizationService;
        _logger = logger;
    }

    [HttpPost("summarize")]
    public async Task<IActionResult> SummarizeRule()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var jsonString = await reader.ReadToEndAsync();
            _logger.LogInformation("Received JSON for summarization, length: {Length}", jsonString?.Length ?? 0);

            if (string.IsNullOrEmpty(jsonString))
            {
                return BadRequest(new { error = "No JSON provided" });
            }

            // Validate it's valid JSON
            JsonDocument.Parse(jsonString);

            var summary = await _summarizationService.SummarizeRuleAsync(jsonString);
            return Ok(new { summary });
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "Invalid JSON received");
            return BadRequest(new { error = $"Invalid JSON: {ex.Message}" });
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("IA ont échoué"))
        {
            _logger.LogError(ex, "Both AI services failed for rule summarization");
            return StatusCode(503, new
            {
                error = "AI services temporarily unavailable. Please try again later.",
                details = "Both Gemini and Groq services are currently experiencing issues."
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during summarization");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    [HttpPost("summarizelogs")]
    public async Task<IActionResult> SummarizeLogs()
    {
        try
        {
            using var reader = new StreamReader(Request.Body);
            var jsonString = await reader.ReadToEndAsync();
            _logger.LogInformation("Received JSON for log summarization, length: {Length}", jsonString?.Length ?? 0);

            if (string.IsNullOrEmpty(jsonString))
            {
                return BadRequest(new { error = "No JSON provided" });
            }

            // Validate it's valid JSON
            JsonDocument.Parse(jsonString);

            var summary = await _summarizationService.SummarizeLogAsync(jsonString);
            return Ok(new { summary });
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "Invalid JSON received");
            return BadRequest(new { error = $"Invalid JSON: {ex.Message}" });
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("IA ont échoué"))
        {
            _logger.LogError(ex, "Both AI services failed for log summarization");
            return StatusCode(503, new
            {
                error = "AI services temporarily unavailable. Please try again later.",
                details = "Both Gemini and Groq services are currently experiencing issues."
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during log summarization");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    [HttpPost("rule-with-ai")]
    public async Task<IActionResult> CreateRule([FromBody] CreateRuleRequestDto request)
    {
        try
        {
            _logger.LogInformation("Received rule creation request: {Prompt}", request?.Prompt);

            if (request == null || string.IsNullOrWhiteSpace(request.Prompt))
            {
                return BadRequest(new { error = "Prompt is required" });
            }

            if (request.Prompt.Length > 1000)
            {
                return BadRequest(new { error = "Prompt is too long. Maximum 1000 characters." });
            }

            var rule = await _summarizationService.CreateRuleAsync(request.Prompt);

            if (rule == null)
            {
                return StatusCode(500, new { error = "Failed to generate rule" });
            }

            return Ok(rule);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument for rule creation");
            return BadRequest(new { error = ex.Message });
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("IA ont échoué"))
        {
            _logger.LogError(ex, "Both AI services failed for rule creation");
            return StatusCode(503, new
            {
                error = "AI services temporarily unavailable. Please try again later.",
                details = "Both Gemini and Groq services are currently experiencing issues. Please check your API keys and try again."
            });
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("token") || ex.Message.Contains("limit"))
        {
            _logger.LogWarning(ex, "Token limit reached for rule creation");
            return StatusCode(429, new
            {
                error = "AI service rate limit reached. Please try again in a few minutes.",
                details = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during rule creation");
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during rule creation");
            return StatusCode(500, new { error = "Internal server error occurred while creating rule" });
        }
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "2.0-dual-ai",
            aiProviders = new[] { "Gemini", "Groq" }
        });
    }

    /// <summary>
    /// New endpoint to check AI service status
    /// </summary>
    [HttpGet("ai-status")]
    public async Task<IActionResult> GetAIStatus()
    {
        try
        {
            // Test both AI services with a simple prompt
            var testPrompt = "Test prompt for service availability";

            var testResult = await _summarizationService.SummarizeRuleAsync(@"{""test"": ""rule""}");

            return Ok(new
            {
                status = "available",
                message = "AI services are operational",
                timestamp = DateTime.UtcNow,
                testResponse = !string.IsNullOrEmpty(testResult)
            });
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("IA ont échoué"))
        {
            return StatusCode(503, new
            {
                status = "unavailable",
                message = "Both AI services are currently unavailable",
                timestamp = DateTime.UtcNow,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking AI service status");
            return StatusCode(500, new
            {
                status = "unknown",
                message = "Unable to determine AI service status",
                timestamp = DateTime.UtcNow
            });
        }
    }
}