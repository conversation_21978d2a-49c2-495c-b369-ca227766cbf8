﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class TypeLocalConfiguration : IEntityTypeConfiguration<TypeLocal>
{
    public void Configure(EntityTypeBuilder<TypeLocal> builder)
    {
        builder.HasKey(s => s.Id);

        //builder.HasMany()
        //.WithOne(s => s.TypeLocal)
        //.HasForeign<PERSON>ey(s => s.TypeLocalId);

    }
}
