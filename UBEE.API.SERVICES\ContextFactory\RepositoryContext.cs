﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using UBEE.MODELS.DataSource;

namespace UBEE.API.SERVICES.ContextFactory;

public class RepositoryContextFactory : IDesignTimeDbContextFactory<UBEEContext>
{
    public UBEEContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json")
            .Build();

        var builder = new DbContextOptionsBuilder<UBEEContext>()
                    .UseSqlServer(configuration.GetConnectionString("sqlConnection"),
                                  b => b.MigrationsAssembly("UBEE.API.SERVICES"));

        return new UBEEContext(builder.Options, null);
    }
}


