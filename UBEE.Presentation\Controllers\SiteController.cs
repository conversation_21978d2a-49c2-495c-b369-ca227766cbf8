﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Presentation.Models;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/site")]
[ApiController]
public class SiteController : BaseController<Site>
{
    private readonly ISiteService _siteService;
    private readonly ILocalService _localService; // Dependency Injection for ILocalService

    public SiteController(ISiteService siteService, ILocalService localService) : base(siteService)
    {
        _siteService = siteService;
        _localService = localService; // Assign the injected ILocalService
        this.UnwantedProperties += "";
        WantedProperties = "";
        IncludesProperties = "Client";
    }

    // Override the base Get method to include LocalsCount
    public override async Task<IActionResult> Get(Guid id)
    {  
        var site = await _siteService.GetById(id);

        if (site == null)
        {
            return NotFound();
        }

        site.LocalsCount = (await _localService.Get(l => l.IdSite == site.Id)).Count();
        return Ok( site.Marshel(WantedProperties, UnwantedProperties));
    }


    [HttpGet("client/{ClientId}")]
    public  async Task<IActionResult> GetByClientId(Guid ClientId)
    {
        var sites = (await this.Service.Get(s => s.ClientId == ClientId, includeProperties: "")).ToList();

        foreach (var site in sites)
        {
            site.LocalsCount = (await _localService.Get(l => l.IdSite == site.Id)).Count();
        }
        UnwantedProperties = "Image,Address,AddressComplement,EmployeesCount,PhoneNumber,Description,Contact,Manager,Email,Status,Grade,Latitude,Longtitude,Surface,Client,Locals,CreatedAt,CreatedBy,LastUpdatedAt,LastUpdatedBy,DeletedAt,DeletedBy";

        return Ok(sites.MarshelList(WantedProperties, UnwantedProperties));
    }

}