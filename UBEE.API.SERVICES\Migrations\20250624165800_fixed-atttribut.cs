﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class fixedatttribut : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ActionType",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CommercialCondition",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "EventType",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GeographicZone",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "MaxControllers",
                table: "ControllerServeur",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MaxSensors",
                table: "ControllerServeur",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TriggerType",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<int>(
                name: "CompanySize",
                table: "Client",
                type: "int",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ActionType",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "CommercialCondition",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "EventType",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "GeographicZone",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "MaxControllers",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "MaxSensors",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "TriggerType",
                table: "ControllerServeur");

            migrationBuilder.AlterColumn<string>(
                name: "CompanySize",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");
        }
    }
}
