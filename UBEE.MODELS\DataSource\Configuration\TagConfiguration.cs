﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class TagConfiguration : IEntityTypeConfiguration<Tag>
{
    public void Configure(EntityTypeBuilder<Tag> builder)
    {
        builder.HasKey(s => s.Id);

        builder.HasMany(s => s.RuleTags)
            .WithOne(s => s.Tag)
            .HasForeignKey(s => s.IdTag);
    }
}