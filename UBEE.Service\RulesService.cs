﻿using UBEE.Contracts.Repository;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Service.Contracts;
using UBEE.MODELS.DataSource.DTOs;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Service;

public sealed class RulesService : BaseService<Rules>, IRulesService
{
    private readonly IRulesRepository _rulesRepository;
    private readonly UBEEContext _context;

    public RulesService(IRepositoryManager unitOfWork, IRulesRepository rulesRepository, UBEEContext context)
        : base(unitOfWork)
    {
        _rulesRepository = rulesRepository;
        _context = context;
    }

    /// <summary>
    /// Retrieves a paginated comprehensive list of all active rules, including statistics and tags.
    /// Uses database-level pagination for optimal performance.
    /// </summary>
    /// <param name="lister">Pagination, sorting, and filtering parameters</param>
    /// <returns>A Pager with RuleComprehensiveDto objects.</returns>
    public async Task<Pager<RuleComprehensiveDto>> GetRulesComprehensiveAsync(Lister<RuleComprehensiveDto> lister)
    {
        return await _rulesRepository.GetRulesComprehensiveAsync(lister);
    }

    /// <summary>
    /// Searches for rules with pagination, sorting, and filtering.
    /// Uses database-level search and pagination for optimal performance.
    /// </summary>
    /// <param name="rulesLister">Search and pagination parameters</param>
    /// <returns>A Pager with matching RuleComprehensiveDto objects.</returns>
    public async Task<Pager<RuleComprehensiveDto>> SearchRulesAsync(RulesLister rulesLister)
    {
        return await _rulesRepository.SearchRulesAsync(rulesLister);
    }

    /// <summary>
    /// Retrieves a comprehensive list of all active rules without pagination (for backward compatibility).
    /// Note: Consider using the paginated version for better performance with large datasets.
    /// </summary>
    /// <returns>A collection of RuleComprehensiveDto objects.</returns>
    public async Task<IEnumerable<RuleComprehensiveDto>> GetRulesComprehensiveAsync()
    {
        // For backward compatibility, create a lister with a large page size
        var lister = new Lister<RuleComprehensiveDto>
        {
            Pagination = new Pagination
            {
                CurrentPage = 1,
                PageSize = int.MaxValue // Get all records
            }
        };

        var result = await _rulesRepository.GetRulesComprehensiveAsync(lister);
        return result.Content;
    }

    /// <summary>
    /// Searches for rules based on a search term (backward compatibility).
    /// Note: Consider using the paginated version for better performance with large datasets.
    /// </summary>
    /// <param name="searchTerm">The term to search for.</param>
    /// <returns>A collection of RuleComprehensiveDto objects matching the search criteria.</returns>
    public async Task<IEnumerable<RuleComprehensiveDto>> SearchRulesAsync(string searchTerm)
    {
        // For backward compatibility, create a RulesLister with a large page size
        var rulesLister = new RulesLister
        {
            SearchTerm = searchTerm,
            IncludeInactive = false,
            Pagination = new Pagination
            {
                CurrentPage = 1,
                PageSize = int.MaxValue // Get all records
            }
        };

        var result = await _rulesRepository.SearchRulesAsync(rulesLister);
        return result.Content;
    }

    /// <summary>
    /// Gets a comprehensive rule by ID
    /// </summary>
    /// <param name="ruleId">Rule identifier</param>
    /// <returns>RuleComprehensiveDto or null if not found</returns>
    public async Task<RuleComprehensiveDto?> GetRuleComprehensiveByIdAsync(Guid ruleId)
    {
        return await _rulesRepository.GetRuleComprehensiveByIdAsync(ruleId);
    }

    /// <summary>
    /// Gets rule client hierarchy
    /// </summary>
    /// <param name="ruleId">Rule identifier</param>
    /// <returns>Collection of RuleClientHierarchyDto</returns>
    public async Task<IEnumerable<RuleClientHierarchyDto>> GetRuleClientHierarchyAsync(Guid ruleId)
    {
        return await _rulesRepository.GetRuleClientHierarchyAsync(ruleId);
    }

    /// <summary>
    /// Gets rule tags
    /// </summary>
    /// <param name="ruleId">Rule identifier</param>
    /// <returns>Collection of RuleWithTagDto</returns>
    public async Task<IEnumerable<RuleWithTagDto>> GetRuleTagsAsync(Guid ruleId)
    {
        return await _rulesRepository.GetRuleTagsAsync(ruleId);
    }
    /// <summary>
    /// Gets transaction details for a rule
    /// </summary>
    /// <param name="ruleId">Rule identifier</param>
    /// <returns>Collection of RuleTransactionDetailDto</returns>
    public async Task<IEnumerable<RuleTransactionDetailDto>> GetRuleTransactionDetailsAsync(Guid ruleId)
    {
        return await _rulesRepository.GetRuleTransactionDetailsAsync(ruleId);
    }



    /// <summary>
    /// Gets actual rule execution logs with pagination
    /// </summary>
    /// <param name="ruleId">Rule identifier</param>
    /// <param name="lister">Pagination, sorting, and filtering parameters</param>
    /// <returns>A Pager with RuleExecutionLogDto objects</returns>
    public async Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionChartDataAsync(Guid ruleId, int days = 30)
    {
        return await _rulesRepository.GetRuleExecutionChartDataAsync(ruleId, days);
    }

    public async Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionHourlyDataAsync(Guid ruleId, DateTime date)
    {
        return await _rulesRepository.GetRuleExecutionHourlyDataAsync(ruleId, date);
    }

    public async Task<IEnumerable<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, int limit = 50)
    {
        return await _rulesRepository.GetRecentRuleExecutionsAsync(ruleId, limit);
    }

    public async Task<Pager<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, Lister<RuleExecutionSimpleDto> lister)
    {
        return await _rulesRepository.GetRecentRuleExecutionsAsync(ruleId, lister);
    }
}
