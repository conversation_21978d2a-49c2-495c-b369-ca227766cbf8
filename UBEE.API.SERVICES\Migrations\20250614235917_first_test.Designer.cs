﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using UBEE.MODELS.DataSource;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    [DbContext(typeof(UBEEContext))]
    [Migration("20250614235917_first_test")]
    partial class first_test
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.17")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Capteur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Capteur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Client", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdOrganisation")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdOrganisation");

                    b.ToTable("Client");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Controller", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Controller");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerController", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdController")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdControllerServeur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdController");

                    b.HasIndex("IdControllerServeur");

                    b.ToTable("ControllerServerController");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdControllerServeur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdRules")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdControllerServeur");

                    b.HasIndex("IdRules");

                    b.ToTable("ControllerServerRule");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServeur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdLicence")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdLicence");

                    b.ToTable("ControllerServeur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Facture", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdLicence")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdLicence");

                    b.ToTable("Facture");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Licence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Licence");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Local", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdSite")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdSite");

                    b.ToTable("Local");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Log", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdCapteur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdController")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdLocal")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdTransaction")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdTransaction");

                    b.ToTable("Log");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Organisation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Organisation");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdRule")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdTag")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdRule");

                    b.HasIndex("IdTag");

                    b.ToTable("RuleTag");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdRule")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdTransaction")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdRule");

                    b.HasIndex("IdTransaction");

                    b.ToTable("RuleTransaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Rules", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Rules");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdClient")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdClient");

                    b.ToTable("Site");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Tag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Tag");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdCapteur")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdController")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("IdLocal")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("InControl")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdCapteur");

                    b.HasIndex("IdController");

                    b.HasIndex("IdLocal");

                    b.ToTable("Transaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.TypeLocal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("IdLocal")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IdLocal");

                    b.ToTable("TypeLocal");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Client", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Organisation", "Organisation")
                        .WithMany("Clients")
                        .HasForeignKey("IdOrganisation")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerController", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Controller", "Controller")
                        .WithMany("ControllerServerControllers")
                        .HasForeignKey("IdController")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.ControllerServeur", "ControllerServeur")
                        .WithMany("ControllerServerControllers")
                        .HasForeignKey("IdControllerServeur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Controller");

                    b.Navigation("ControllerServeur");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServerRule", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.ControllerServeur", "ControllerServeur")
                        .WithMany("ControllerServerRules")
                        .HasForeignKey("IdControllerServeur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Rules", "Rules")
                        .WithMany("ControllerServerRules")
                        .HasForeignKey("IdRules")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ControllerServeur");

                    b.Navigation("Rules");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServeur", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Licence", "Licence")
                        .WithMany("ControllerServeurs")
                        .HasForeignKey("IdLicence")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Licence");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Facture", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Licence", "Licence")
                        .WithMany("Factures")
                        .HasForeignKey("IdLicence")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Licence");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Local", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Site", "Site")
                        .WithMany("Locals")
                        .HasForeignKey("IdSite")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Log", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Transaction", "Transaction")
                        .WithMany("Logs")
                        .HasForeignKey("IdTransaction")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTag", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Rules", "Rule")
                        .WithMany("RuleTags")
                        .HasForeignKey("IdRule")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Tag", "Tag")
                        .WithMany("RuleTags")
                        .HasForeignKey("IdTag")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rule");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.RuleTransaction", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Rules", "Rule")
                        .WithMany("RuleTransactions")
                        .HasForeignKey("IdRule")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Transaction", "Transaction")
                        .WithMany("RuleTransactions")
                        .HasForeignKey("IdTransaction")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rule");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Site", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Client", "Client")
                        .WithMany("Sites")
                        .HasForeignKey("IdClient")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Transaction", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Capteur", "Capteur")
                        .WithMany("Transactions")
                        .HasForeignKey("IdCapteur")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Controller", "Controller")
                        .WithMany("Transactions")
                        .HasForeignKey("IdController")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("UBEE.MODELS.DataSource.Models.Local", "Local")
                        .WithMany("Transactions")
                        .HasForeignKey("IdLocal")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capteur");

                    b.Navigation("Controller");

                    b.Navigation("Local");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.TypeLocal", b =>
                {
                    b.HasOne("UBEE.MODELS.DataSource.Models.Local", "Local")
                        .WithMany("TypeLocals")
                        .HasForeignKey("IdLocal")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Local");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Capteur", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Client", b =>
                {
                    b.Navigation("Sites");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Controller", b =>
                {
                    b.Navigation("ControllerServerControllers");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.ControllerServeur", b =>
                {
                    b.Navigation("ControllerServerControllers");

                    b.Navigation("ControllerServerRules");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Licence", b =>
                {
                    b.Navigation("ControllerServeurs");

                    b.Navigation("Factures");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Local", b =>
                {
                    b.Navigation("Transactions");

                    b.Navigation("TypeLocals");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Organisation", b =>
                {
                    b.Navigation("Clients");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Rules", b =>
                {
                    b.Navigation("ControllerServerRules");

                    b.Navigation("RuleTags");

                    b.Navigation("RuleTransactions");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Site", b =>
                {
                    b.Navigation("Locals");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Tag", b =>
                {
                    b.Navigation("RuleTags");
                });

            modelBuilder.Entity("UBEE.MODELS.DataSource.Models.Transaction", b =>
                {
                    b.Navigation("Logs");

                    b.Navigation("RuleTransactions");
                });
#pragma warning restore 612, 618
        }
    }
}
