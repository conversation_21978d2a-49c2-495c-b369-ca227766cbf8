﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/rule-transaction")]
[ApiController]
public class RuleTransactionController : BaseController<RuleTransaction>
{
    private readonly IRuleTransactionService _service;
    public RuleTransactionController(IRuleTransactionService baseService) : base(baseService)
    {
        this._service = baseService as IRuleTransactionService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}

