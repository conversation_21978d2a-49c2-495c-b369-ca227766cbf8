﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.Common.Models;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Presentation.Controllers.Base;


public interface IBaseController<TEntity> where TEntity : BaseClass
{
    Task<IActionResult> GetAll();
    Task<IActionResult> Search([FromBody] Lister<TEntity> lister);
    Task<IActionResult> Count();
    IActionResult GenerateReference();
    Task<IActionResult> Get(Guid id);
    Task<IActionResult> Descriptor(string param);
    Task<IActionResult> ExistBy(string param);
    Task<IActionResult> Add([FromBody] TEntity entity);
    Task<IActionResult> Update([FromBody] TEntity entity, string propertiesToCheck = "");
    Task<IActionResult> Delete(Guid id);
    IActionResult Options();
}