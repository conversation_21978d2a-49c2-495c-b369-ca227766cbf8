﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;
using UBEE.Shared.DataTransferObjects.Dto;

namespace UBEE.Presentation.Controllers;

[Route("api/capteur")]
[ApiController]
public class CapteurController : BaseController<Capteur>
{
    private readonly ICapteurService _service;
    private readonly UBEEContext _context;
    public CapteurController(ICapteurService baseService, UBEEContext context) : base(baseService)
    {
        this._service = baseService as ICapteurService;
        this.UnwantedProperties += "";
        IncludesProperties = "Controller,TypeCapteur";
        _context = context;
    }

    [HttpGet("site/{idsite}")]
    public async Task<IActionResult> GetCapteursViewBySiteId(Guid idsite)
    {
        var results = await _context.vwsCapteurClientSite
        .Where(v => v.SiteId == idsite)
        .ToListAsync();
        return Ok(results);
    }


    [HttpGet("client/{clientid}")]
    public async Task<IActionResult> GetCapteursViewByClientId(Guid clientid)
    {
        var results = await _context.vwsCapteurClientSite
        .Where(v => v.ClientId == clientid)
        .ToListAsync();

        return Ok(results);

    }


    [HttpGet("local/{localid}/controllerId/{controllerId}")]
    public async Task<IActionResult> GetCapteursByLocalIdAndControllerID(Guid localid, Guid controllerId)
    {
        var capteurs = await _context.Capteur
            .AsNoTracking()
            .Where(c => c.Transactions.Any(t =>
                t.IdLocal == localid &&
                t.IdController == controllerId &&
                t.InControl == true))
            .Select(c => new
            {
                c.Id,
                c.Protocol,
                c.Manufacturer,
                c.Model,
                c.ModelIdentifier,
                c.FriendlyName,
                c.DisplayName,
                c.LastSeen,
                c.IeeeAddress,
                c.NetworkAddress,
                c.Endpoint,
                c.NodeId,
                c.HomeId,
                c.SecurityClasses,
                c.IdTypeCapteur,
                c.Topic,
                c.State,
                c.Brand,
                c.ControllerId,
            })
            .ToListAsync();

        return Ok(capteurs);
    }


    [HttpGet("local/{localid}")]
    public async Task<IActionResult> GetCapteursViewByLocalId(Guid localid)
    {
        var results = await _context.vwsCapteurClientSite
            .Where(v => v.LocalId == localid)
            .ToListAsync();

        return Ok(results);

    }

    [HttpPut("change-state")]
    public async Task<IActionResult> changeCapteurState([FromBody] Capteur capteurPayload)
    {
        try
        {
            var capteur = await _context.Set<Capteur>().FirstOrDefaultAsync(s => s.Topic == capteurPayload.Topic);
            if (capteur == null)
                return NotFound("capteur not found");
            capteur.State = capteurPayload.State;
            capteur.LastSeen = DateTime.Now; 
            _context.Entry(capteur).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return Ok(capteur);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Capteur processing failed: {ex.Message}");
        }
    }

    [HttpPut("change-displayname/{idCapteur}")]
    public async Task<IActionResult> changeDisplayName(Guid idCapteur, [FromBody] Capteur capteurPayload)
    {
        try
        {
            var capteur = await _context.Set<Capteur>().FirstOrDefaultAsync(s => s.Id == idCapteur);
            if (capteur == null)
                return NotFound("capteur not found");
            capteur.DisplayName = capteurPayload.DisplayName;
            _context.Entry(capteur).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return Ok(capteur);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Capteur processing failed: {ex.Message}");
        }
    }

    [HttpPost("{type}/{id?}")]
    public async Task<IActionResult> GetCapteursByType(string type, Guid? id, [FromBody] PaginationDto payload)
    {
        int pageSize = payload.PageSize ?? 10;
        int pageNumber = payload.PageNumber ?? 1;
        int skip = (pageNumber - 1) * pageSize;
        string search = payload.SearchTerm?.ToLower() ?? "";

        // Build base query
        var query = _context.vwsCapteurClientSite.AsQueryable();

        // Filter by type
        query = type.ToLower() switch
        {
            "client" => query.Where(v => v.ClientId == id && v.InControl == true),
            "site" => query.Where(v => v.SiteId == id && v.InControl == true),
            "local" => query.Where(v => v.LocalId == id && v.InControl == true),
            "controller" => query.Where(v => v.ControllerId == id && v.InControl == true),
            "all" => query.Where(v => v.InControl == true),
            _ => throw new ArgumentException("Invalid filter type. Use 'client', 'site', or 'local'.")
        };

        // Apply search
        if (!string.IsNullOrWhiteSpace(search))
        {
            query = query.Where(v =>
                v.FriendlyName.ToLower().Contains(search) ||
                v.DisplayName.ToLower().Contains(search) ||
                v.Model.ToLower().Contains(search) ||
                v.Brand.ToLower().Contains(search));
        }

        // Total count
        var totalCount = await query.CountAsync();

        // Paginated results
        var paginatedResults = await query
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();

        // Return result
        return Ok(new
        {
            TotalCount = totalCount,
            PageSize = pageSize,
            PageNumber = pageNumber,
            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
            Data = paginatedResults
        });
    }

}

