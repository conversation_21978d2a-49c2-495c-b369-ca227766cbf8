﻿using System.Linq.Expressions;
using System.Reflection;

namespace UBEE.Shared.GenericListModel.Filtering;


public static class Filtering
{
    public class WhereParams
    {
        public string Column { get; set; }
        public object Value { get; set; }
        public string Op { get; set; }
        public string AndOr { get; set; }
    }

    /// <summary>
    /// Make a predicate from the specified <paramref name="wheres"/>.
    /// </summary>
    public static Expression<Func<T, bool>> ToPredciate<T>(this IEnumerable<WhereParams> wheres)
    {
        var list = wheres.ToList();

        if ((list.Count == 4 || list.Count == 5)
        && list[0].AndOr == "AND"
        && list[1].AndOr == "AND"
        && list[2].AndOr == "OR"
        && list[3].AndOr == "AND")
        {
            var param = Expression.Parameter(typeof(T), "x");

            // Left group: filter[0] AND filter[1]
            var leftGroup = Expression.AndAlso(
                GetComparePredicateBody(param, list[0]),
                GetComparePredicateBody(param, list[1])
            );

            // Right group: filter[2] OR filter[3]
            var rightGroup = Expression.OrElse(
                GetComparePredicateBody(param, list[2]),
                GetComparePredicateBody(param, list[3])
            );

            // Combine: leftGroup AND rightGroup
            var finalBody = Expression.AndAlso(leftGroup, rightGroup);
            if (list.Count > 4)
                finalBody = list[4].AndOr == "AND" ? Expression.AndAlso(finalBody, GetComparePredicateBody(param, list[4])) : Expression.OrElse(finalBody, GetComparePredicateBody(param, list[4])); ;


            return Expression.Lambda<Func<T, bool>>(finalBody, param);
        }

        using (var e = wheres.GetEnumerator())
        {
            if (!e.MoveNext()) // not filtered
                return x => true;

            var pe = Expression.Parameter(typeof(T), "x");
            var body = GetComparePredicateBody(pe, e.Current); // first condition

            // join body with more conditions
            while (e.MoveNext())
            {
                var right = GetComparePredicateBody(pe, e.Current);
                switch (e.Current.AndOr)
                {
                    case "AND":
                        body = Expression.AndAlso(body, right);
                        break;
                    case "OR":
                        body = Expression.OrElse(body, right);
                        break;
                    default:
                        // LessThan and Equal don't make much sense on booleans, do they?
                        throw new Exception("Bad boolean operator.");
                }
            }

            return Expression.Lambda<Func<T, bool>>(body, pe);
        }
    }

    private static Expression GetComparePredicateBody(Expression x, WhereParams where)
    {
        // Example: suppose 'where.Column' is a property of type Guid or Guid?
        // and where.Value is a string that can be parsed into a Guid.

        // 1) Get the property Expression
        var property = Expression.Property(x, where.Column);

        // 2) Figure out the actual CLR type of that property
        Type propertyType = ((PropertyInfo)((MemberExpression)property).Member).PropertyType;

        // We can parse the string to a Guid if propertyType is Guid or Nullable<Guid>
        Guid guidValue;
        bool isGuidProperty = propertyType == typeof(Guid) || propertyType == typeof(Guid?);
        bool parsedGuid = Guid.TryParse(where.Value?.ToString(), out guidValue);

        // 3) Construct the 'right' side of the expression
        Expression right;
        if (isGuidProperty && parsedGuid)
        {
            // If the property is nullable, we can use Expression.Constant(guidValue, typeof(Guid?))
            // or convert to match property type:
            if (propertyType == typeof(Guid?))
                right = Expression.Constant((Guid?)guidValue, typeof(Guid?));
            else
                right = Expression.Constant(guidValue, typeof(Guid));
        }
        else
        {
            // Fallback: maybe handle as string or int or date or throw an error...
            right = Expression.Constant(where.Value?.ToString());
        }

        return where.Op switch
        {
            "eq" => Expression.Equal(property, right),
            "neq" => Expression.NotEqual(property, right),
            "lt" => Expression.LessThan(property, right),
            "gt" => Expression.GreaterThan(property, right),
            "lte" => Expression.LessThanOrEqual(property, right),
            "gte" => Expression.GreaterThanOrEqual(property, right),
            "contains" =>
                propertyType == typeof(string)
                    ? Expression.Call(property, typeof(string).GetMethod("Contains", new[] { typeof(string) }), right)
                    : throw new ArgumentException("Contains operator is only valid for string properties."),
            _ => throw new ArgumentException("Bad comparison operator."),
        };
    }

    public static IQueryable<T> Where<T>(this IQueryable<T> source, IEnumerable<WhereParams> wheres) => source.Where(wheres.ToPredciate<T>());
    public static IEnumerable<T> Where<T>(this IEnumerable<T> source, IEnumerable<WhereParams> wheres) => source.Where(wheres.ToPredciate<T>().Compile());
}
