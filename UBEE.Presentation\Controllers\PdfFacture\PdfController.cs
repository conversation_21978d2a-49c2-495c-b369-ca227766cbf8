using Microsoft.AspNetCore.Mvc;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace UBEE.Presentation.PdfFacture;

[ApiController]
[Route("api/[controller]")]
public class PdfController : ControllerBase
{
    public PdfController()
    { 
        QuestPDF.Settings.License = LicenseType.Community;
        QuestPDF.Settings.EnableDebugging = true;
    }

    [HttpGet("generate")]
    public IActionResult GenerateInvoice()
    {
        var document = Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(40);
                page.DefaultTextStyle(x => x.FontSize(10).FontFamily("Arial"));

                page.Content().Column(column =>
                {
                    // Header Section
                    column.Item().Row(row =>
                    {
                        // Left side - Facture title
                        row.RelativeItem().Column(leftHeaderColumn =>
                        {
                            leftHeaderColumn.Item().Text("Facture")
                                .FontSize(28).Bold().FontColor("#333333");
                            leftHeaderColumn.Item().Text($"Numéro de facture : 320199")
                                .FontSize(12).Italic().FontColor("#666666");
                        });

                        // Right side - Company logo area with green background
                        row.ConstantItem(120).Height(80).Padding(10)
                            .AlignCenter().AlignMiddle()
                            .Image("./wwwroot/logoIOT.png")
                            .FitArea();
                    });

                    column.Item().PaddingTop(20);

                    // Company Information
                    column.Item().Text("Your pro builders, 83 cours Jean Jaurès, 33800 Bordeaux, France")
                        .FontSize(9).FontColor("#666666");

                    column.Item().PaddingTop(30);

                    // Invoice Details Section
                    column.Item().Row(row =>
                    {
                        // Left side - Bill to information
                        row.RelativeItem().Column(leftColumn =>
                        {
                            leftColumn.Item().Text("FACTURÉ À").Bold().FontSize(9);
                            leftColumn.Item().PaddingTop(5).Column(billToDetails =>
                            {
                                billToDetails.Item().Text("Votre client").Bold();
                                billToDetails.Item().Text("10 Place de la Gare");
                                billToDetails.Item().Text("33800 Bordeaux");
                                billToDetails.Item().Text("France");
                            });
                        });

                        // Right side - Invoice details
                        row.RelativeItem().AlignRight().Column(rightColumn =>
                        {
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Facture N°:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text("2022029").Bold();
                            });
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Date:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text("10/8/2023");
                            });
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Échéance:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text("24/8/2023");
                            });
                            rightColumn.Item().PaddingTop(10).Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("N° de référence:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text("2022029");
                            });
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Date de livraison:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text("10/8/2023");
                            });
                        });
                    });

                    column.Item().PaddingTop(30);

                    // Colored Invoice Summary Bar
                    column.Item().Row(row =>
                    {
                        row.ConstantItem(100).Height(40).Background("#6BBF59").Padding(8)
                            .Column(facNumCol =>
                            {
                                facNumCol.Item().Text("Facture N°").FontColor("#FFFFFF").FontSize(9);
                                facNumCol.Item().Text("2022029").FontColor("#FFFFFF").Bold();
                            });

                        row.ConstantItem(100).Height(40).Background("#7ACC68").Padding(8)
                            .Column(dateCol =>
                            {
                                dateCol.Item().Text("Date d'émission").FontColor("#FFFFFF").FontSize(9);
                                dateCol.Item().Text("10. 8. 2023").FontColor("#FFFFFF").Bold();
                            });

                        row.ConstantItem(100).Height(40).Background("#8BD975").Padding(8)
                            .Column(dueCol =>
                            {
                                dueCol.Item().Text("date d'échéance").FontColor("#FFFFFF").FontSize(9);
                                dueCol.Item().Text("24. 8. 2023").FontColor("#FFFFFF").Bold();
                            });

                        row.RelativeItem().Height(40).Background("#333333").Padding(8)
                            .Column(totalCol =>
                            {
                                totalCol.Item().Text("Total à payer (EUR)").FontColor("#FFFFFF").FontSize(9);
                                totalCol.Item().Text("7 900,00 €").FontColor("#FFFFFF").Bold();
                            });
                    });

                    column.Item().PaddingTop(30);

                    // Invoice Items Table
                    column.Item().Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(4); // Description
                            columns.RelativeColumn(2); // Prix
                            columns.RelativeColumn(2); // Montant
                        });

                        // Table Header
                        table.Header(header =>
                        {
                            header.Cell().Background("#F5F5F5").Padding(8).BorderBottom(1)
                                .Text("Description").Bold().FontSize(9);
                            header.Cell().Background("#F5F5F5").Padding(8).BorderBottom(1)
                                .Text("Prix (€)").Bold().FontSize(9);
                            header.Cell().Background("#F5F5F5").Padding(8).BorderBottom(1)
                                .Text("Montant (€)").Bold().FontSize(9);
                        });

                        // Table Rows
                        var items = new[]
                        {
                            new { Description = "Installation d'électricité",  Price = "300,00", Amount = "1 500,00" },
                            new { Description = "Installation de chauffage",  Price = "250,00", Amount = "1 000,00" },
                            new { Description = "Carreaux",  Price = "25,00", Amount = "5 000,00" },
                            new { Description = "Placement au sol",  Price = "80,00", Amount = "400,00" }
                        };

                        foreach (var item in items)
                        {
                            table.Cell().BorderBottom(1).BorderColor("#E0E0E0").Padding(8)
                                .Text(item.Description).FontSize(9);
                            table.Cell().BorderBottom(1).BorderColor("#E0E0E0").Padding(8)
                                .Text(item.Price).FontSize(9);
                            table.Cell().BorderBottom(1).BorderColor("#E0E0E0").Padding(8)
                                .Text(item.Amount).FontSize(9);
                        }

                        // Total Row
                        table.Cell().Background("#F5F5F5").Padding(8)
                            .Text("Montant total (EUR):").Bold().FontSize(9);
                        table.Cell().Background("#F5F5F5").Padding(8).Text("");
                        table.Cell().Background("#F5F5F5").Padding(8)
                            .Text("7 900,00 €").Bold().FontSize(9);
                    });

                    // Footer with company details
                    column.Item().PaddingTop(40);
                    column.Item().BorderTop(1).BorderColor("#E0E0E0").PaddingTop(10)
                        .Text("Your pro builders")
                        .FontSize(9).FontColor("#666666");
                    column.Item().Text("83 cours Jean Jaurès")
                        .FontSize(9).FontColor("#666666");
                    column.Item().Text("33800 Bordeaux")
                        .FontSize(9).FontColor("#666666");
                    column.Item().Text("France")
                        .FontSize(9).FontColor("#666666");
                });
            });
        });

        var pdfBytes = document.GeneratePdf();
        return File(pdfBytes, "application/pdf", "facture-2022029.pdf");
    }

    [HttpPost("generate-custom")]
    public IActionResult GenerateCustomInvoice([FromBody] InvoiceData invoiceData)
    {
        var document = Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(40);
                page.DefaultTextStyle(x => x.FontSize(10).FontFamily("Arial"));

                page.Content().Column(column =>
                {
                    // Header Section
                    column.Item().Row(row =>
                    {
                        // Left side - Facture title
                        row.RelativeItem().Column(leftHeaderColumn =>
                        {
                            leftHeaderColumn.Item().Text("Facture")
                                .FontSize(28).Bold().FontColor("#333333");
                            leftHeaderColumn.Item().Text($"Numéro de facture : {invoiceData.InvoiceNumber}")
                                .FontSize(12).Italic().FontColor("#666666");
                        });

                        row.ConstantItem(120).Height(80).Padding(10)
                            .AlignCenter().AlignMiddle()
                            .Image("./wwwroot/logoIOT.png")
                            .FitArea();
                    });

                    column.Item().PaddingTop(20);

                    // Company Information
                    column.Item().Text("IOTEVA, 123 Avenue de la Technologie, 75001 Paris, France")
                        .FontSize(9).FontColor("#666666");

                    column.Item().PaddingTop(30);

                    // Invoice Details Section
                    column.Item().Row(row =>
                    {
                        // Left side - Bill to information
                        row.RelativeItem().Column(leftColumn =>
                        {
                            leftColumn.Item().Text("FACTURÉ À").Bold().FontSize(9);
                            leftColumn.Item().PaddingTop(5).Column(billToDetails =>
                            {
                                billToDetails.Item().Text(invoiceData.Recipient.Name).Bold();
                                billToDetails.Item().Text(invoiceData.Recipient.Email);
                                billToDetails.Item().Text(invoiceData.Recipient.Address);
                                billToDetails.Item().Text(invoiceData.Recipient.CityStateZip);
                            });
                        });

                        // Right side - Invoice details
                        row.RelativeItem().AlignRight().Column(rightColumn =>
                        {
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Facture N°:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text(invoiceData.InvoiceNumber).Bold();
                            });
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Date:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text(invoiceData.Date);
                            });
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Échéance:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text(invoiceData.DueDate);
                            });
                            rightColumn.Item().PaddingTop(10).Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("N° de référence:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text(invoiceData.InvoiceNumber);
                            });
                            rightColumn.Item().Row(detailRow =>
                            {
                                detailRow.ConstantItem(80).Text("Date de livraison:").FontSize(9);
                                detailRow.ConstantItem(80).AlignRight().Text(invoiceData.Date);
                            });
                        });
                    });

                    column.Item().PaddingTop(30);

                    // Calculate totals
                    var subtotal = invoiceData.Items.Sum(x => x.Total);

                    // Colored Invoice Summary Bar
                    column.Item().Row(row =>
                    {
                        row.ConstantItem(100).Height(40).Background("#6BBF59").Padding(8)
                            .Column(facNumCol =>
                            {
                                facNumCol.Item().Text("Facture N°").FontColor("#FFFFFF").FontSize(9);
                                facNumCol.Item().Text(invoiceData.InvoiceNumber).FontColor("#FFFFFF").Bold();
                            });

                        row.ConstantItem(100).Height(40).Background("#7ACC68").Padding(8)
                            .Column(dateCol =>
                            {
                                dateCol.Item().Text("Date d'émission").FontColor("#FFFFFF").FontSize(9);
                                dateCol.Item().Text(invoiceData.Date).FontColor("#FFFFFF").Bold();
                            });

                        row.ConstantItem(100).Height(40).Background("#8BD975").Padding(8)
                            .Column(dueCol =>
                            {
                                dueCol.Item().Text("date d'échéance").FontColor("#FFFFFF").FontSize(9);
                                dueCol.Item().Text(invoiceData.DueDate).FontColor("#FFFFFF").Bold();
                            });

                        row.RelativeItem().Height(40).Background("#333333").Padding(8)
                            .Column(totalCol =>
                            {
                                totalCol.Item().Text("Total à payer (EUR)").FontColor("#FFFFFF").FontSize(9);
                                totalCol.Item().Text($"{subtotal:N2} €").FontColor("#FFFFFF").Bold();
                            });
                    });

                    column.Item().PaddingTop(30);

                    // Invoice Items Table
                    column.Item().Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(4); // Description
                            columns.RelativeColumn(2); // Prix
                        });

                        // Table Header
                        table.Header(header =>
                        {
                            header.Cell().Background("#F5F5F5").Padding(8).BorderBottom(1)
                                .Text("Description").Bold().FontSize(9);
                            header.Cell().Background("#F5F5F5").Padding(8).BorderBottom(1)
                                .Text("Prix (€)").Bold().FontSize(9);
                        });

                        // Table Rows
                        foreach (var item in invoiceData.Items)
                        {
                            table.Cell().BorderBottom(1).BorderColor("#E0E0E0").Padding(8)
                                .Text($"{item.Description:N2}").FontSize(9);  
                            table.Cell().BorderBottom(1).BorderColor("#E0E0E0").Padding(8)
                                .Text($"{item.Total:N2}").FontSize(9);
                        }
                        // Total Row
                        table.Cell().Background("#F5F5F5").Padding(8).Text("");
                        table.Cell().Background("#F5F5F5").Padding(8)
                            .Text($"{subtotal:N2} €").Bold().FontSize(9);
                    });

                    // Footer with company details
                    column.Item().PaddingTop(40);
                    column.Item().BorderTop(1).BorderColor("#E0E0E0").PaddingTop(10)
                        .Text("IOTEVA")
                        .FontSize(9).FontColor("#666666");
                    column.Item().Text("123 Avenue de la Technologie")
                        .FontSize(9).FontColor("#666666");
                    column.Item().Text("75001 Paris, France")
                        .FontSize(9).FontColor("#666666");
                    column.Item().Text("<EMAIL>")
                        .FontSize(9).FontColor("#666666");
                });
            });
        });

        var pdfBytes = document.GeneratePdf();
        return File(pdfBytes, "application/pdf", $"facture-{invoiceData.InvoiceNumber}.pdf");
    }
}

public class InvoiceData
{
    public string Date { get; set; } = "10. 8. 2023";
    public string DueDate { get; set; } = "24. 8. 2023";
    public string InvoiceNumber { get; set; } = "2022029";
    public ContactInfo Recipient { get; set; } = new();
    public List<InvoiceItem> Items { get; set; } = new();
}

public class ContactInfo
{
    public string Name { get; set; } = "";
    public string Email { get; set; } = "";
    public string Address { get; set; } = "";
    public string CityStateZip { get; set; } = "";
}

public class InvoiceItem
{
    public string Description { get; set; } = "";
    public decimal UnitPrice { get; set; }
    // public int Quantity { get; set; }
    public decimal Total => UnitPrice;
}