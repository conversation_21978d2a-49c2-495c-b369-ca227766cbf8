﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class SubscriptionConfiguration : IEntityTypeConfiguration<Subscription>
{
    public void Configure(EntityTypeBuilder<Subscription> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.Client)
        .WithMany()
        .HasForeignKey(s => s.ClientId);

        builder.HasOne(s => s.Licence)
        .WithMany(s => s.Subscriptions)
        .HasForeignKey(s => s.LicenceId);

        builder.HasMany(s => s.SubscribedOptions)
             .WithOne(s => s.Subscription)
             .HasForeignKey(s => s.SubscriptionId);
    }
}
