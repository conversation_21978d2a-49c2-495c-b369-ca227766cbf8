﻿namespace UBEE.MODELS.DataSource.DTOs
{
    public class RuleComprehensiveDto
    {
        public Guid RuleId { get; set; }
        public bool Enabled { get; set; }
        public string? RuleSummary { get; set; }


        public int Priority { get; set; }
        public string RawData { get; set; }
        public DateTime RuleCreatedAt { get; set; }
        public DateTime? RuleLastUpdatedAt { get; set; } // Nullable

        // Statistics from vw_RulesWithStats
        public int TotalClients { get; set; }
        public int TotalSites { get; set; }
        public int TotalLocals { get; set; }
        public DateTime? LastTriggered { get; set; } // Nullable

        public string Status { get; set; } // "active" or "inactive"

        // This field will be populated by joining with vw_RulesWithTagsString
        public string TagsString { get; set; }
    }
}