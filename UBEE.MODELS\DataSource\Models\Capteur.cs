﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Capteur : AuditEntity
{
    public string? DisplayName { get; set; }
    public string Protocol { get; set; }   // "Zigbee" or "ZWave"
    public string Manufacturer { get; set; }
    public string? Model { get; set; }
    public string ModelIdentifier { get; set; }
    public string FriendlyName { get; set; }
    public DateTime LastSeen { get; set; }
    /// Zigbee‐specific details.
    public string IeeeAddress { get; set; }    // e.g. "0x00158d0001dc126a"
    public int NetworkAddress { get; set; }    // e.g. 48723
    public int Endpoint { get; set; }
    // Z-wave
    public int NodeId { get; set; }
    public int HomeId { get; set; }
    public string SecurityClasses { get; set; }
    public List<SensorReading> SensorReadings { get; set; }
    public List<Transaction> Transactions { get; set; }
    public Guid IdTypeCapteur { get; set; }
    public TypeCapteur? TypeCapteur { get; set; }
    public string? RowData { get; set; }
    public string? Topic { get; set; }
    public string? State { get; set; }
    public string? Brand{ get; set; }
    public Guid? ControllerId { get; set; }
    public Controller? Controller { get; set; }

}
