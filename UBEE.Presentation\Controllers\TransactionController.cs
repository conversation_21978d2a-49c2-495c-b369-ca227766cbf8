﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;


namespace UBEE.Presentation.Controllers;

[Route("api/transaction")]
[ApiController]
public class TransactionController : BaseController<Transaction>
{
    private readonly ITransactionService _service;

    public TransactionController(ITransactionService baseService) : base(baseService)
    {
        this._service = baseService as ITransactionService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }

    /// <summary>
    /// Gets a list of transactions filtered by local ID.
    /// </summary>
    /// <param name="id_local">The ID of the local entity.</param>
    /// <returns>A list of transactions.</returns>
    [HttpGet("local/{id_local}")]

    public virtual async Task<IActionResult> GetTransactionsByLocalId(Guid id_local)
    {
        // Using the Get method from IBaseService to filter by IdLocal
        var transactions = await _service.Get(t => t.IdLocal == id_local);

        // Convert to List to use .Any() for check, or safely enumerate
        var transactionList = transactions?.ToList();

        if (transactionList == null || !transactionList.Any())
        {
            return NotFound($"No transactions found for local ID: {id_local}");
        }

        return Ok(transactionList);
    }

    /// <summary>
    /// Gets a list of transactions filtered by controller ID.
    /// </summary>
    /// <param name="id_controller">The ID of the controller entity.</param>
    /// <returns>A list of transactions.</returns>
    [HttpGet("controller/{id_controller}")]
    public virtual async Task<IActionResult> GetTransactionsByControllerId(Guid id_controller)
    {
        // Using the Get method from IBaseService to filter by IdController
        var transactions = await _service.Get(t => t.IdController == id_controller);

        var transactionList = transactions?.ToList();

        if (transactionList == null || !transactionList.Any())
        {
            return NotFound($"No transactions found for controller ID: {id_controller}");
        }

        return Ok(transactionList);
    }

    /// <summary>
    /// Gets a list of transactions filtered by capteur (sensor) ID.
    /// </summary>
    /// <param name="id_capteur">The ID of the capteur (sensor) entity.</param>
    /// <returns>A list of transactions.</returns>
    [HttpGet("capteur/{id_capteur}")]

    public virtual async Task<IActionResult> GetTransactionsByCapteurId(Guid id_capteur)
    {
        // Using the Get method from IBaseService to filter by IdCapteur
        // As noted before, ensure Transaction model has an IdCapteur property
        // or adjust the lambda if it's a many-to-many relationship (e.g., t.Capteurs.Any(c => c.Id == id_capteur))
        var transactions = await _service.Get(t => t.IdCapteur == id_capteur);

        var transactionList = transactions?.ToList();

        if (transactionList == null || !transactionList.Any())
        {
            return NotFound($"No transactions found for capteur ID: {id_capteur}");
        }

        return Ok(transactionList);
    }
    [HttpGet("local/{id_local}/controller/{id_controller}")]

    public virtual async Task<IActionResult> GetByLocalAndController(Guid id_local, Guid id_controller)
    {
        // Using the Get method from IBaseService to filter by IdCapteur
        // As noted before, ensure Transaction model has an IdCapteur property
        // or adjust the lambda if it's a many-to-many relationship (e.g., t.Capteurs.Any(c => c.Id == id_capteur))
        var transactions = await _service.Get(t => t.IdLocal == id_local && t.IdController == id_controller);

        var transactionList = transactions?.ToList();

        if (transactionList == null || !transactionList.Any())
        {
            return NotFound($"No transactions found for local ID: {id_local} && controller ID = {id_controller}");
        }

        return Ok(transactionList);
    }
    [HttpGet("local/{id_local}/capteur/{id_capteur}")]

    public virtual async Task<IActionResult> GetByLocalAndCapteu(Guid id_local, Guid id_capteur)
    {
        // Using the Get method from IBaseService to filter by IdCapteur
        // As noted before, ensure Transaction model has an IdCapteur property
        // or adjust the lambda if it's a many-to-many relationship (e.g., t.Capteurs.Any(c => c.Id == id_capteur))
        var transactions = await _service.Get(t => t.IdLocal == id_local && t.IdCapteur == id_capteur);

        var transactionList = transactions?.ToList();

        if (transactionList == null || !transactionList.Any())
        {
            return NotFound($"No transactions found for local ID: {id_local} && capteur ID = {id_capteur}");
        }

        return Ok(transactionList);
    }
    [HttpGet("controller/{id_controller}/capteur/{id_capteur}")]

    public virtual async Task<IActionResult> GetByControllerAndCapteu(Guid id_controller, Guid id_capteur)
    {
        // Using the Get method from IBaseService to filter by IdCapteur
        // As noted before, ensure Transaction model has an IdCapteur property
        // or adjust the lambda if it's a many-to-many relationship (e.g., t.Capteurs.Any(c => c.Id == id_capteur))
        var transactions = await _service.Get(t => t.IdController == id_controller && t.IdCapteur == id_capteur);

        var transactionList = transactions?.ToList();

        if (transactionList == null || !transactionList.Any())
        {
            return NotFound($"No transactions found for local ID: {id_controller} && capteur ID = {id_capteur}");
        }

        return Ok(transactionList);
    }


}