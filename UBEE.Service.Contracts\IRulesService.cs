﻿// IRulesService.cs - Add this to your UBEE.Service.Contracts namespace

using UBEE.MODELS.DataSource.DTOs;
using UBEE.MODELS.DataSource.Models;
using UBEE.Shared.GenericListModel.Filtering;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace UBEE.Service.Contracts
{
    public interface IRulesService : IBaseService<Rules>
    {
        // Paginated methods using your existing generic models
        Task<Pager<RuleComprehensiveDto>> GetRulesComprehensiveAsync(Lister<RuleComprehensiveDto> lister);
        Task<Pager<RuleComprehensiveDto>> SearchRulesAsync(RulesLister rulesLister);

        // Non-paginated methods (for backward compatibility)
        Task<IEnumerable<RuleComprehensiveDto>> GetRulesComprehensiveAsync();
        Task<IEnumerable<RuleComprehensiveDto>> SearchRulesAsync(string searchTerm);

        // Existing methods
        Task<RuleComprehensiveDto> GetRuleComprehensiveByIdAsync(Guid ruleId);
        Task<IEnumerable<RuleTransactionDetailDto>> GetRuleTransactionDetailsAsync(Guid ruleId);

        Task<IEnumerable<RuleClientHierarchyDto>> GetRuleClientHierarchyAsync(Guid ruleId);
        Task<IEnumerable<RuleWithTagDto>> GetRuleTagsAsync(Guid ruleId);

        Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionChartDataAsync(Guid ruleId, int days = 30);
        Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionHourlyDataAsync(Guid ruleId, DateTime date);
        Task<IEnumerable<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, int limit = 50);
        Task<Pager<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, Lister<RuleExecutionSimpleDto> lister);

    }
}