﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class changedlicencerelations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ControllerServeur_Licence_IdLicence",
                table: "ControllerServeur");

            migrationBuilder.RenameColumn(
                name: "IdLicence",
                table: "ControllerServeur",
                newName: "SubscriptionId");

            migrationBuilder.RenameIndex(
                name: "IX_ControllerServeur_IdLicence",
                table: "ControllerServeur",
                newName: "IX_ControllerServeur_SubscriptionId");

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "Option",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Value",
                table: "Option",
                type: "int",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ControllerServeur_Subscription_SubscriptionId",
                table: "ControllerServeur",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ControllerServeur_Subscription_SubscriptionId",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Option");

            migrationBuilder.DropColumn(
                name: "Value",
                table: "Option");

            migrationBuilder.RenameColumn(
                name: "SubscriptionId",
                table: "ControllerServeur",
                newName: "IdLicence");

            migrationBuilder.RenameIndex(
                name: "IX_ControllerServeur_SubscriptionId",
                table: "ControllerServeur",
                newName: "IX_ControllerServeur_IdLicence");

            migrationBuilder.AddForeignKey(
                name: "FK_ControllerServeur_Licence_IdLicence",
                table: "ControllerServeur",
                column: "IdLicence",
                principalTable: "Licence",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
