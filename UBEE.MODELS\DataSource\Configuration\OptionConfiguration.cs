﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class OptionConfiguration : IEntityTypeConfiguration<Option>
{
    public void Configure(EntityTypeBuilder<Option> builder)
    {
        builder.HasKey(s  => s.Id);
        //builder.HasMany(s => s.SubscribedOptions)
        //     .WithOne(s => s.Option)
        //     .HasForeign<PERSON>ey(s => s.OptionId);
    }
}
