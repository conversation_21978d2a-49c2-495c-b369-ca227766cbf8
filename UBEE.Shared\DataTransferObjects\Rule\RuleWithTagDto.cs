﻿namespace UBEE.MODELS.DataSource.DTOs
{
    public class RuleWithTagDto
    {
        public Guid RuleId { get; set; }
        public bool Enabled { get; set; }
        public int Priority { get; set; }
        public string RawData { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUpdatedAt { get; set; } // Nullable
        public Guid? TagId { get; set; } // Nullable if a rule might have no tags
        public DateTime? TagCreatedAt { get; set; } // Nullable
        public string TagName { get; set; }
        public bool? TagIsActive { get; set; } // Nullable if TagId is null
    }
}