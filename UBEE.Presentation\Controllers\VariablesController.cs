﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/variables")]
[ApiController]
public class VariablesController : BaseController<Variables>
{
    private readonly IVariablesService _service;
    public VariablesController(IVariablesService baseService) : base(baseService)
    {
        this._service = baseService as IVariablesService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}


