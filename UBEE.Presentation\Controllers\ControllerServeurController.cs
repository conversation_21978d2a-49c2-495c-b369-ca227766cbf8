﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;
using UBEE.Shared.GenericListModel.Filtering;
using UBEE.Shared.Views;

namespace UBEE.Presentation.Controllers;

[Route("api/controller-serveur")]
[ApiController]
public class ControllerServeurController : BaseController<ControllerServeur>
{
    private readonly IControllerServeurService _service;
    private readonly UBEEContext _context;
    public ControllerServeurController(IControllerServeurService baseService, UBEEContext context) : base(baseService)
    {
        this._service = baseService as IControllerServeurService;
        this.UnwantedProperties += "";
        IncludesProperties = "ControllerServerControllers.Controller";
        _context = context;
    }

    [HttpPost("update-all-max-values")]
    public async Task<IActionResult> UpdateAllMaxValues()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Get all ControllerServeur with their related data
        var controllerServeurs = await _context.ControllerServeur
            .Include(cs => cs.ControllerServerControllers)
                .ThenInclude(csc => csc.Controller)
                    .ThenInclude(c => c.Transactions)
                        .ThenInclude(t => t.Capteur)
            .ToListAsync();

        if (!controllerServeurs.Any())
            return Ok(new { Message = "No ControllerServeur records found", UpdatedCount = 0 });

        var updateResults = new List<object>();
        var updatedCount = 0;

        foreach (var controllerServeur in controllerServeurs)
        {
            // Count Controllers
            var controllerCount = controllerServeur.ControllerServerControllers
                .Select(csc => csc.Controller)
                .Distinct()
                .Count();

            // Count unique Sensors (Capteurs)
            var sensorCount = controllerServeur.ControllerServerControllers
                .SelectMany(csc => csc.Controller.Transactions)
                .Where(t => t.Capteur != null)
                .Select(t => t.IdCapteur)
                .Distinct()
                .Count();

            // Update only if values have changed
            if (controllerServeur.MaxControllers != controllerCount ||
                controllerServeur.MaxSensors != sensorCount)
            {
                var oldMaxControllers = controllerServeur.MaxControllers;
                var oldMaxSensors = controllerServeur.MaxSensors;

                controllerServeur.MaxControllers = controllerCount;
                controllerServeur.MaxSensors = sensorCount;
                updatedCount++;

                updateResults.Add(new
                {
                    Id = controllerServeur.Id,
                    Name = controllerServeur.Name,
                    Previous = new { MaxControllers = oldMaxControllers, MaxSensors = oldMaxSensors },
                    Updated = new { MaxControllers = controllerCount, MaxSensors = sensorCount }
                });
            }
        }

        // Save all changes at once
        if (updatedCount > 0)
        {
            await _context.SaveChangesAsync();
        }

        stopwatch.Stop();

        return Ok(new
        {
            Message = $"Successfully updated max values for {updatedCount} ControllerServeur records",
            UpdatedCount = updatedCount,
            TotalRecords = controllerServeurs.Count,
            ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
            UpdatedRecords = updateResults
        });
    }

    [HttpGet("client/{idclient}")]
    public async Task<IActionResult> GetControllerServersByClientId(Guid idclient)
    {
        var results = await _context.ClientActiveControllerServers
        .Where(v => v.ClientId == idclient)
        .ToListAsync();
        return Ok(results);
    }

    [HttpGet("get-all-light")]
    public async Task<ActionResult<IEnumerable<object>>> GetLightControllerServeurs()
    {
        var servers = await _context.ControllerServeur
            .AsNoTracking()
            .Select(cs => new
            {
                cs.Id,
                cs.Name,
                cs.Status
            })
            .ToListAsync();

        return Ok(servers);
    }

    [HttpGet("get-light-by-client/{clientId}")]
    public async Task<ActionResult<IEnumerable<object>>> GetLightControllerServeursByClient(Guid clientId)
    {
        var servers = await _context.ControllerServeur
            .AsNoTracking()
            .Where(cs => cs.Subscription != null && cs.Subscription.ClientId == clientId)
            .Select(cs => new
            {
                cs.Id,
                cs.Name,
                cs.Status,
                cs.SubscriptionId
            })
            .ToListAsync();

        return Ok(servers);
    }

    [HttpGet("by-client/{clientId}")]
    public async Task<ActionResult<IEnumerable<ControllerServerHierarchyDto>>> GetHierarchyByClient(Guid clientId)
    {
        var result = await _context.ControllerServerHierarchy
            .Where(x => x.ClientId == clientId)
            .ToListAsync();
        return Ok(result);
    }

    [HttpGet("generate-code")]
    public async Task<ActionResult<string>> GenerateUniqueControllerServeurCode()
    {
        const string prefix = "HOC";
        string newCode;
        bool exists;

        var random = new Random();

        do
        {
            int randomNumber = random.Next(100000, 1000000); // 6-digit number
            newCode = $"{prefix}-{randomNumber}";

            // Check if it already exists in the database
            exists = await _context.ControllerServeur.AnyAsync(cs => cs.Name == newCode);

        } while (exists);

        return Ok(newCode);
    }

    [HttpGet("subscription/{subscriptionId}/can-add")]
    public async Task<ActionResult<bool>> CanAddControllerServeur(Guid subscriptionId)
    {
        // Get the current count of ControllerServeurs for the subscription
        var currentCount = await _context.ControllerServeur
            .CountAsync(cs => cs.SubscriptionId == subscriptionId);

        // Get the max allowed from LicenceOptions > Option
        var maxValue = await _context.Subscription
            .Where(s => s.Id == subscriptionId)
            .SelectMany(s => s.Licence.LicenceOptions)
            .Where(lo => lo.Option.Type == "max-controllers-server")
            .Select(lo => lo.Option.Value)
            .FirstOrDefaultAsync();

        if (maxValue == 0)
        {
            return Ok(true); // No max set, allow by default
        }

        if (currentCount >= maxValue)
        {
            return BadRequest($"Vous avez atteint la limite de {maxValue} contrôleurs serveurs pour cette licence.");
        }

        return Ok(true);
    }

    [HttpPost("paginate-controller-servers")]
    public async Task<IActionResult> PaginateControllersView([FromQuery] Guid clientId, Lister<ClientActiveControllerServer> lister)
    {
        Pager<ClientActiveControllerServer> pager = new Pager<ClientActiveControllerServer>();

        var query = _context.ClientActiveControllerServers
            .Where(v => v.ClientId == clientId)
            .AsQueryable();

        if (lister.SortParams != null && lister.SortParams.Any())
        {
            foreach (var sort in lister.SortParams)
            {
                query = query.OrderByProperty(sort.Column, sort.Sort == "asc");
            }
        }
        else
        {
            query = query.OrderBy(x => x.Id);
        }

        if (lister.FilterParams != null)
        {
            var wheres = lister.FilterParams.ToArray();
            query = query.Where(wheres);
        }

        lister.Pagination.TotalElement = await query.CountAsync();

        if (lister.Pagination != null)
        {
            var skip = (lister.Pagination.CurrentPage - lister.Pagination.StartIndex) * lister.Pagination.PageSize;
            if (lister.Pagination.TotalElement <= lister.Pagination.PageSize)
            {
                lister.Pagination.CurrentPage = lister.Pagination.StartIndex;
                skip = 0;
            }

            if (skip > 0)
                query = query.Skip(skip);

            var take = lister.Pagination.PageSize;
            query = query.Take(take);

            lister.Pagination.PageCount = (int)Math.Ceiling((double)lister.Pagination.TotalElement / lister.Pagination.PageSize);
            lister.Pagination.IsLast = lister.Pagination.CurrentPage == lister.Pagination.PageCount;
            lister.Pagination.IsFirst = lister.Pagination.CurrentPage == 1;
        }

        pager.Content = await query.ToListAsync();
        pager.Lister = lister;

        return Ok(pager);
    }
}

