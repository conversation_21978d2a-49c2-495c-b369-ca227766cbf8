﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class fixedlocal : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "NombreCapteurs",
                table: "Local",
                newName: "SensorsCount");

            migrationBuilder.RenameColumn(
                name: "Etage",
                table: "Local",
                newName: "Floor");

            migrationBuilder.RenameColumn(
                name: "CapacitePersonnes",
                table: "Local",
                newName: "Capacity");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "SensorsCount",
                table: "Local",
                newName: "NombreCapteurs");

            migrationBuilder.RenameColumn(
                name: "Floor",
                table: "Local",
                newName: "Etage");

            migrationBuilder.RenameColumn(
                name: "Capacity",
                table: "Local",
                newName: "CapacitePersonnes");
        }
    }
}
