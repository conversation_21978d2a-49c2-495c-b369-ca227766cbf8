﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class LocalConfiguration : IEntityTypeConfiguration<Local>
{
    public void Configure(EntityTypeBuilder<Local> builder)
    {
        builder.HasKey(s => s.Id);

        builder.HasOne(s => s.Site)
        .WithMany()
        .HasForeignKey(s => s.IdSite)
        .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(s => s.TypeLocal)
            .WithMany()
            .HasForeignKey(s => s.TypeLocalId);

        //builder.HasMany(s => s.Transactions)
        //.WithOne(s => s.Local)
        //.HasForeignKey(s => s.IdLocal);
    }
}
