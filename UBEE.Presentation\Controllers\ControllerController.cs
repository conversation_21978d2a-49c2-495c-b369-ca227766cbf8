﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Runtime;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;
using UBEE.Shared.GenericListModel.Filtering;
using UBEE.Shared.Views;

namespace UBEE.Presentation.Controllers;

[Route("api/Controller")]
[ApiController]
public class ControllerController : BaseController<Controller>
{
    private readonly IControllerService _service;
    private readonly UBEEContext _context;
    public ControllerController(IControllerService baseService, UBEEContext context) : base(baseService)
    {
        this._service = baseService as IControllerService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
        _context = context;
    }

    [HttpPut("set-base-topic/{id}")]
    public async Task<IActionResult> SetBaseTopic(Guid id)
    {
        var controller = await _context.Controller.FindAsync(id);
        if (controller == null)
        {
            return NotFound($"Controller with id {id} not found.");
        }

        controller.BaseTopic = controller.Id.ToString();

        _context.Controller.Update(controller);
        await _context.SaveChangesAsync();

        return Ok(new { message = "BaseTopic set successfully", baseTopic = controller.BaseTopic });
    }


    [HttpGet("local/{localId}")]
    public async Task<ActionResult<IEnumerable<object>>> GetControllersByLocal(Guid localId)
    {
        var controllers = await _context.Controller
            .AsNoTracking()
            .Where(c => c.Transactions.Any(t => t.IdLocal == localId))
            .Select(c => new
            {
                c.Id,
                c.HostName,
                c.Model,
                c.SerialNumber,
                c.MacAddress,
                c.IpAddress,
                c.LastConnection,
                c.State,
                c.BaseTopic,
                c.InstallationDate,
                c.CreatedAt,
                c.CreatedBy,
                c.LastUpdatedAt,
                c.LastUpdatedBy,
                c.DeletedAt,
                c.DeletedBy
            })
            .ToListAsync();

        return Ok(controllers);
    }

    [HttpGet("get-light-by-client/{clientId}")]
    public async Task<ActionResult<IEnumerable<object>>> GetLightControllersByClient(Guid clientId)
    {
        var controllers = await _context.Controller
            .AsNoTracking()
            .Where(cs => !cs.ControllerServerControllers.Any())
            .Select(cs => new
            {
                cs.Id,
                cs.HostName,
                cs.State
            })
            .ToListAsync();

        return Ok(controllers);
    }

    [HttpGet("subscription/{subscriptionId}/can-add")]
    public async Task<ActionResult<bool>> CanAddController(Guid subscriptionId)
    {
        // Get the number of Controllers already assigned to that Subscription
        var currentCount = await _context.ControllerServerController
            .Where(csc => csc.ControllerServeur.SubscriptionId == subscriptionId)
            .Select(csc => csc.IdController)
            .Distinct()
            .CountAsync();

        // Get the max allowed from LicenceOptions > Option
        var maxValue = await _context.Subscription
            .Where(s => s.Id == subscriptionId)
            .SelectMany(s => s.Licence.LicenceOptions)
            .Where(lo => lo.Option.Type == "max-controllers")
            .Select(lo => lo.Option.Value)
            .FirstOrDefaultAsync();

        if (maxValue == 0)
        {
            return Ok(true); // No max set, allow by default
        }

        if (currentCount >= maxValue)
        {
            return BadRequest($"Vous avez atteint la limite de {maxValue} contrôleurs pour cette licence.");
        }

        return Ok(true);
    }

    [HttpGet("client-controllers/{idclient}")]
    public async Task<IActionResult> GetControllersByClientId(Guid idclient)
    {
        var results = await _context.ClientLicenceControllerViews
        .Where(v => v.ClientId == idclient)
        .ToListAsync();
        return Ok(results);
    }

    //[HttpPost("paginate-controllers")]
    //public async Task<IActionResult> PaginateControllersView([FromQuery] Guid clientId, Lister<ClientLicenceControllerView> lister)
    //{
    //    Pager<ClientLicenceControllerView> pager = new Pager<ClientLicenceControllerView>();

    //    var query = _context.ClientLicenceControllerViews
    //    .Where(v => v.ClientId == clientId)
    //    .AsQueryable();
    //    var controllerId = typeof(ClientLicenceControllerView).GetProperty("ControllerId");

    //    if(lister.SortParams != null && lister.SortParams.Any())
    //    {
    //        foreach(var sort in lister.SortParams)
    //        {
    //            query = query.OrderByProperty(sort.Column, sort.Sort == "asc");
    //        }
    //    }
    //    else
    //    {
    //        query = query.OrderBy(x => x.ControllerId);
    //    }

    //    if (!(lister.FilterParams is null))
    //    {
    //        var wheres = new Filtering.WhereParams[lister.FilterParams.Count()];

    //        for (int i = 0; i < wheres.Length; i++)
    //        {
    //            wheres[i] = lister.FilterParams[i];
    //        }

    //        query = query.Where(wheres);
    //    }

    //    lister.Pagination.TotalElement = await query.CountAsync();

    //    if (lister.Pagination != null)
    //    {
    //        var skip = (lister.Pagination.CurrentPage - lister.Pagination.StartIndex) * lister.Pagination.PageSize;
    //        if (query.Count() <= lister.Pagination.PageSize)
    //        {
    //            lister.Pagination.CurrentPage = lister.Pagination.StartIndex;
    //            skip = 0;
    //        }
    //        if (skip > 0)
    //            query = lister.SortParams == null || !lister.SortParams.Any()
    //                ? query.AsEnumerable().OrderBy(x => controllerId?.GetValue(x, null)).AsQueryable().Skip(skip)
    //                : query.Skip(skip);
    //        var take = query.Count() < lister.Pagination.PageSize ? query.Count() : lister.Pagination.PageSize;

    //        lister.Pagination.PageCount = !query.Any() ? 1 : lister.Pagination.TotalElement % lister.Pagination.PageSize != 0
    //            ? (lister.Pagination.TotalElement / lister.Pagination.PageSize) + 1
    //            : (lister.Pagination.TotalElement / lister.Pagination.PageSize);

    //        if (take > 0)
    //            query = query.Take(take);

    //        lister.Pagination.IsLast = lister.Pagination.CurrentPage == lister.Pagination.PageCount;
    //        lister.Pagination.IsFirst = lister.Pagination.CurrentPage == 1;
    //    }



    //    pager.Content = await query.ToListAsync();

    //    pager.Lister = lister;

    //    return Ok(pager);
    //}
    [HttpPost("paginate-controllers")]
    public async Task<IActionResult> PaginateControllersView([FromQuery] Guid clientId, Lister<ClientLicenceControllerView> lister)
    {
        Pager<ClientLicenceControllerView> pager = new Pager<ClientLicenceControllerView>();

        var query = _context.ClientLicenceControllerViews
            .Where(v => v.ClientId == clientId)
            .AsQueryable();

        if (lister.SortParams != null && lister.SortParams.Any())
        {
            foreach (var sort in lister.SortParams)
            {
                query = query.OrderByProperty(sort.Column, sort.Sort == "asc");
            }
        }
        else
        {
            query = query.OrderBy(x => x.ControllerId);
        }

        if (lister.FilterParams != null)
        {
            var wheres = lister.FilterParams.ToArray();
            query = query.Where(wheres);
        }

        lister.Pagination.TotalElement = await query.CountAsync();

        if (lister.Pagination != null)
        {
            var skip = (lister.Pagination.CurrentPage - lister.Pagination.StartIndex) * lister.Pagination.PageSize;
            if (lister.Pagination.TotalElement <= lister.Pagination.PageSize)
            {
                lister.Pagination.CurrentPage = lister.Pagination.StartIndex;
                skip = 0;
            }

            if (skip > 0)
                query = query.Skip(skip);

            var take = lister.Pagination.PageSize;
            query = query.Take(take);

            lister.Pagination.PageCount = (int)Math.Ceiling((double)lister.Pagination.TotalElement / lister.Pagination.PageSize);
            lister.Pagination.IsLast = lister.Pagination.CurrentPage == lister.Pagination.PageCount;
            lister.Pagination.IsFirst = lister.Pagination.CurrentPage == 1;
        }

        pager.Content = await query.ToListAsync();
        pager.Lister = lister;

        return Ok(pager);
    }

    [HttpGet("generate-code")]
    public async Task<ActionResult<string>> GenerateUniqueControllerCode()
    {
        const string prefix = "C";
        string newCode;
        bool exists;

        var random = new Random();

        do
        {
            int randomNumber = random.Next(100000, 1000000); // 6-digit number
            newCode = $"{prefix}-{randomNumber}";

            // Check if it already exists in the database
            exists = await _context.ControllerServeur.AnyAsync(cs => cs.Name == newCode);

        } while (exists);

        return Ok(newCode);
    }

}

