﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/sensor-reading")]
[ApiController]
public class SensorReadingController : BaseController<SensorReading>
{
    private readonly ISensorReadingService _service;
    public SensorReadingController(ISensorReadingService baseService) : base(baseService)
    {
        this._service = baseService as ISensorReadingService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}