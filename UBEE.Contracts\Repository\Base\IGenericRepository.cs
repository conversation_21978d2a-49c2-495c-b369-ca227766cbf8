﻿using System.Collections.Generic;
using System.Linq.Expressions;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Contracts.Repository.Base;


public interface IGenericRepository { }

/// <summary>
/// The IGenericRepository interface
/// </summary>
/// <typeparam name="TEntity">TEntity</typeparam>
public interface IGenericRepository<TEntity> : IGenericRepository where TEntity : class
{
    /// <summary>
    /// The generic delete method by identifier
    /// </summary>
    /// <param name="id">identifier</param>
    Task<bool> Delete(object id);

    /// <summary>
    /// The generic delete method
    /// </summary>
    /// <param name="entityToDelete">entit y To Delete</param>
    TEntity Delete(TEntity entityToDelete);

    /// <summary>
    /// Detach Object
    /// </summary>
    /// <param name="entityToDetach">entity To Detach</param>
    void DetachObject(TEntity entityToDetach);


    /// <summary>
    /// The generic get all with filter method
    /// </summary>
    /// <param name="lister">lister</param>
    /// <param name="includeProperties"></param>
    /// <returns>Pager<TEntity></returns>
    Task<Pager<TEntity>> GetPages(Lister<TEntity> lister, string includeProperties = "", IQueryable<TEntity> query = null);


    /// <summary>
    /// The generic count method
    /// </summary>
    /// <returns>int</returns>
    Task<int> Count();


    /// <summary>
    /// The generic get entity by parent entity method
    /// </summary>
    /// <param name="includeProperties">properties to include</param>
    /// <param name="entityName"></param>
    /// <param name="id">lister</param>
    /// <returns>TEntity</returns>
    Task<TEntity> GetByChildId(Guid id, string includeProperties, string entityName);

    /// <summary>
    /// The generic get all entities by child entity method
    /// </summary>
    /// <param name="id">child id</param>
    /// <param name="entityName">child entity name</param>
    /// <param name="orderBy">order By property</param>
    /// <param name="orderDesc">desc option</param>
    /// <param name="includeProperties">properties to include</param>
    /// <returns>TEntity</returns>
    Task<IEnumerable<TEntity>> GetAllByChildId(Guid id, string entityName, string orderBy, bool orderDesc, string includeProperties);


    /// <summary>
    /// The generic get method
    /// </summary>
    /// <param name="filter">filter</param>
    /// <param name="orderBy">orderBy</param>
    /// <param name="includeProperties">include Properties</param>
    /// <returns>IEnumerable<TEntity></returns>
    Task<IEnumerable<TEntity>> Get(System.Linq.Expressions.Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, string includeProperties = "", int skip = 0, int take = 0);



    /// <summary>
    /// The generic get by id method
    /// </summary>
    /// <param name="id">identifier</param>
    /// <returns>TEntity</returns>
    Task<TEntity> GetById(object id);


    /// <summary>
    /// Get the first element and null if it doesn't exists
    /// </summary>
    /// <returns>TEntity</returns>
    Task<TEntity> GetFirstOrDefault();

    /// <summary>
    /// The generic get by id method
    /// </summary>
    /// <param name="id">identifier</param>
    /// <param name="includeProperties">include Properties</param>
    /// <returns>TEntity</returns>
    Task<TEntity> GetOne(object id, string includeProperties = "");

    /// <summary>
    /// The generic insert method
    /// </summary>
    /// <param name="entity">TEntity</param>
    Task<TEntity> Insert(TEntity entity);

    /// <summary>
    /// The generic update method
    /// </summary>
    /// <param name="propertiesToCheck"></param>
    /// <param name="entityToUpdate">entity To Update</param>
    Task<TEntity> Update(TEntity entityToUpdate, string propertiesToCheck);

    /// <summary>
    /// The generic update values method.
    /// </summary>
    /// <param name="entityToUpdate">entity to update.</param>
    TEntity UpdateValues(TEntity entityToUpdate);

    /// <summary>
    /// Gets items by query
    /// </summary>
    /// <param name="sqlQuery">query</param>
    /// <param name="parameters">params of query</param>
    /// <returns>Returm items</returns>
    IEnumerable<TEntity> GetItemsByQuery(string sqlQuery, params object[] parameters);

    /// <summary>
    /// Execute query
    /// </summary>
    /// <param name="sqlQuery">query</param>
    /// <param name="parameters">params of query</param>
    /// <returns></returns>
    void ExecuteQueryWithoutResult(string sqlQuery, params object[] parameters);

    /// <summary>
    /// Update List of entities
    /// </summary>
    /// <param name="entityTiesUpdate">Entities to update</param>
    void Update(IEnumerable<TEntity> entitiesToUpdate);

    /// <summary>
    /// Delete List of entities
    /// </summary>
    /// <param name="entityTiesToDelete">Entities to delete</param>
    void Delete(IEnumerable<TEntity> entityTiesToDelete);

    /// <summary>
    /// Insert List of entities 
    /// </summary>
    /// <param name="entityTiesUpdate">Entities to update</param>
    void Insert(IEnumerable<TEntity> entitiesToInsert);

    /// <summary>
    /// Generate a Reference for entities
    /// </summary>
    /// <param name="prefix"></param>
    /// <returns></returns>
    string GenerateReference(string prefix = null);

    /// <summary>
    /// Gets items by query
    /// </summary>
    /// <param name="sqlQuery">query</param>
    /// <param name="parameters">params of query</param>
    /// <returns>Gets items count by query</returns>
    IEnumerable<T> GetItemsCountByQuery<T>(string sqlQuery, params object[] parameters);
    TEntity CheckIfExistByProperty(string propertyName, object value);
    Task<int> GetCountAsync(Expression<Func<TEntity, bool>> expression);
    Task<IEnumerable<TEntity>> InsertRange(List<TEntity> entities);
}
