﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class AddedAttributs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "TypeLocal",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Nom",
                table: "TypeLocal",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Nom",
                table: "Tag",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContactDeSite",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<byte[]>(
                name: "Images",
                table: "Site",
                type: "varbinary(max)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<bool>(
                name: "IsEnabled",
                table: "Site",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Nom",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Pays",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Rue",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "TelephoneSurSite",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Ville",
                table: "Site",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "RuleTag",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "Enabled",
                table: "Rules",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "Priority",
                table: "Rules",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "RawData",
                table: "Rules",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Nom",
                table: "Organisation",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Message",
                table: "Log",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<byte[]>(
                name: "Architecture2DImage",
                table: "Local",
                type: "varbinary(max)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<int>(
                name: "CapacitePersonnes",
                table: "Local",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Local",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Etage",
                table: "Local",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<byte[]>(
                name: "ImageLocal",
                table: "Local",
                type: "varbinary(max)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<string>(
                name: "Nom",
                table: "Local",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "NombreCapteurs",
                table: "Local",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<double>(
                name: "Surface",
                table: "Local",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<DateOnly>(
                name: "DateDebut",
                table: "Licence",
                type: "date",
                nullable: false,
                defaultValue: new DateOnly(1, 1, 1));

            migrationBuilder.AddColumn<DateOnly>(
                name: "DateFin",
                table: "Licence",
                type: "date",
                nullable: false,
                defaultValue: new DateOnly(1, 1, 1));

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Licence",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Key",
                table: "Licence",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "Facture",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<double>(
                name: "Total",
                table: "Facture",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "IpAdresse",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Nom",
                table: "ControllerServeur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Controller",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Nom",
                table: "Controller",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsEnabled",
                table: "Client",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<byte[]>(
                name: "LogoOrganisation",
                table: "Client",
                type: "varbinary(max)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<string>(
                name: "NomComplet",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "NombreEquipementsActif",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NombreEquipementsInactif",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NombreLocaux",
                table: "Client",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Password",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PasswordConfirmed",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Telephone",
                table: "Client",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "TypeLocal");

            migrationBuilder.DropColumn(
                name: "Nom",
                table: "TypeLocal");

            migrationBuilder.DropColumn(
                name: "Nom",
                table: "Tag");

            migrationBuilder.DropColumn(
                name: "ContactDeSite",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Images",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "IsEnabled",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Nom",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Pays",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Rue",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "TelephoneSurSite",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Ville",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "RuleTag");

            migrationBuilder.DropColumn(
                name: "Enabled",
                table: "Rules");

            migrationBuilder.DropColumn(
                name: "Priority",
                table: "Rules");

            migrationBuilder.DropColumn(
                name: "RawData",
                table: "Rules");

            migrationBuilder.DropColumn(
                name: "Nom",
                table: "Organisation");

            migrationBuilder.DropColumn(
                name: "Message",
                table: "Log");

            migrationBuilder.DropColumn(
                name: "Architecture2DImage",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "CapacitePersonnes",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "Etage",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "ImageLocal",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "Nom",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "NombreCapteurs",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "Surface",
                table: "Local");

            migrationBuilder.DropColumn(
                name: "DateDebut",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "DateFin",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "Key",
                table: "Licence");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "Facture");

            migrationBuilder.DropColumn(
                name: "Total",
                table: "Facture");

            migrationBuilder.DropColumn(
                name: "IpAdresse",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "Nom",
                table: "ControllerServeur");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "Nom",
                table: "Controller");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "IsEnabled",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "LogoOrganisation",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NomComplet",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreEquipementsActif",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreEquipementsInactif",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "NombreLocaux",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Password",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "PasswordConfirmed",
                table: "Client");

            migrationBuilder.DropColumn(
                name: "Telephone",
                table: "Client");
        }
    }
}
