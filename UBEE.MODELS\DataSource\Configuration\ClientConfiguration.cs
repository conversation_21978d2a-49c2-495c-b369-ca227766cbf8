﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class ClientConfiguration : IEntityTypeConfiguration<Client>
{
    public void Configure(EntityTypeBuilder<Client> builder)
    {
        builder.HasKey(s => s.Id);

        builder.HasOne(s => s.Organisation)
            .WithMany()
            .HasForeignKey(s => s.IdOrganisation).IsRequired(false);

        //builder.HasMany(s => s.Subscriptions)
        //    .WithOne(s => s.Client)
        //    .HasForeignKey(s => s.ClientId).IsRequired(false);

        //builder.HasMany(s => s.Sites)
        //    .WithOne(s => s.Client)
        //    .HasForeignKey(s => s.ClientId).IsRequired(false);
    }
}
