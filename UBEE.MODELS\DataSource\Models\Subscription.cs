﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Subscription : AuditEntity
{
    public DateTime? DateDebut { get; set; }
    public DateTime? DateFin { get; set; }
    public string? Status { get; set; }
    public Guid ClientId { get; set; }
    public Client Client { get; set; }
    public Guid LicenceId { get; set; }
    public Licence Licence { get; set; }
    public float? Price { get; set; }
    public string? PaymentFrequency { get; set; }
    public List<Facture> Factures { get; set; }
    public List<SubscribedOptions> SubscribedOptions { get; set; }
    public List<ControllerServeur> ControllerServeurs { get; set; }

}
