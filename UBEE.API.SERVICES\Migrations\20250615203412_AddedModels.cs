﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class AddedModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Endpoint",
                table: "Capteur",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "FriendlyName",
                table: "Capteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "HomeId",
                table: "Capteur",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "IeeeAddress",
                table: "Capteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "LastSeen",
                table: "Capteur",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Manufacturer",
                table: "Capteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ModelIdentifier",
                table: "Capteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "NetworkAddress",
                table: "Capteur",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NodeId",
                table: "Capteur",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Protocol",
                table: "Capteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SecurityClasses",
                table: "Capteur",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "SensorReading",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IdCapteur = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Battery = table.Column<int>(type: "int", nullable: true),
                    LinkQuality = table.Column<int>(type: "int", nullable: true),
                    Temperature = table.Column<double>(type: "float", nullable: true),
                    Humidity = table.Column<double>(type: "float", nullable: true),
                    Illuminance = table.Column<double>(type: "float", nullable: true),
                    Power = table.Column<double>(type: "float", nullable: true),
                    Energy = table.Column<double>(type: "float", nullable: true),
                    Voltage = table.Column<double>(type: "float", nullable: true),
                    Current = table.Column<double>(type: "float", nullable: true),
                    Occupancy = table.Column<bool>(type: "bit", nullable: true),
                    Tamper = table.Column<bool>(type: "bit", nullable: true),
                    Contact = table.Column<bool>(type: "bit", nullable: true),
                    BatteryLevel = table.Column<int>(type: "int", nullable: true),
                    Value = table.Column<double>(type: "float", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastUpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SensorReading", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SensorReading_Capteur_IdCapteur",
                        column: x => x.IdCapteur,
                        principalTable: "Capteur",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SensorReading_IdCapteur",
                table: "SensorReading",
                column: "IdCapteur");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SensorReading");

            migrationBuilder.DropColumn(
                name: "Endpoint",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "FriendlyName",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "HomeId",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "IeeeAddress",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "LastSeen",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "Manufacturer",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "ModelIdentifier",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "NetworkAddress",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "NodeId",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "Protocol",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "SecurityClasses",
                table: "Capteur");
        }
    }
}
