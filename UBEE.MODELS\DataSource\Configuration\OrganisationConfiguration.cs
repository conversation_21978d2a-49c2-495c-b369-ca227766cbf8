﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class OrganisationConfiguration : IEntityTypeConfiguration<Organisation>
{
    public void Configure(EntityTypeBuilder<Organisation> builder)
    {
        builder.HasKey(s => s.Id);

        //builder.HasMany(s => s.Clients)
        //    .WithOne(s => s.Organisation)
        //    .HasForeignKey(s => s.IdOrganisation);
    }
}
