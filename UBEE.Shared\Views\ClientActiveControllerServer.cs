﻿namespace UBEE.Shared.Views;

public class ClientActiveControllerServer
{
    // ControllerServeur info
    public Guid Id { get; set; }
    public string Name { get; set; }
    public int MaxControllers { get; set; }
    public int MaxSensors { get; set; }
    public string GeographicZone { get; set; }
    public string CommercialCondition { get; set; }
    public string TriggerType { get; set; }
    public string ActionType { get; set; }
    public string EventType { get; set; }
    public string Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdatedAt { get; set; }

    // Client info
    public Guid ClientId { get; set; }
    public string ClientName { get; set; }

    // Licence info
    public Guid LicenceId { get; set; }
    public string LicenceName { get; set; }
    public string LicenceDescription { get; set; }

    // Subscription info
    public Guid SubscriptionId { get; set; }
    public DateTime? SubscriptionStartDate { get; set; }
    public DateTime? SubscriptionEndDate { get; set; }
    public string SubscriptionStatus { get; set; }
}
