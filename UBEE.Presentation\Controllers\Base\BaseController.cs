﻿using Microsoft.AspNetCore.Mvc;
using System.Dynamic;
using UBEE.MODELS.Common.Models;
using UBEE.Presentation.Models;
using UBEE.Service.Contracts;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Presentation.Controllers.Base;


public abstract class BaseController<TEntity> : ControllerBase, IBaseController<TEntity> where TEntity : BaseClass
{
    //neasted object or properties to add to the Entity
    protected string IncludesProperties = "";

    // properties to remove from JSON Object
    protected string UnwantedProperties = "CreatedBy,UpdatedAt,UpdatedBy,DeletedBy,DeletedAt,LastUpdatedAt,LastUpdatedBy";

    // properties to leave in JSON Object
    protected string WantedProperties = "";


    protected readonly IBaseService<TEntity> Service;

    protected BaseController(IBaseService<TEntity> baseService)
    {
        Service = baseService;
    }

    [HttpGet]
    //  [Authorize]
    public virtual async Task<IActionResult> GetAll()
    {
        return Ok((await Service.Get(includeProperties: IncludesProperties)).MarshelList(WantedProperties, UnwantedProperties));
    }

    [HttpPost("search")]
    public virtual async Task<IActionResult> Search([FromBody] Lister<TEntity> lister)
    {
        return Ok(await Search(lister, false));
    }

    private async Task<IDictionary<string, object>> Search(Lister<TEntity> lister, bool simpleGraph)
    {
        var pager = simpleGraph ? await Service.GetPages(lister) : await Service.GetPages(lister, IncludesProperties);
        IDictionary<string, object> expando = new ExpandoObject();
        expando.Add("Content", pager.Content.MarshelList(WantedProperties, UnwantedProperties));
        expando.Add("Lister", pager.Lister);
        return expando;
    }

    [HttpGet("count")]
    public virtual async Task<IActionResult> Count()
    {
        return Ok(Service.Count());
    }

    [HttpGet("GenerateReference")]
    public IActionResult GenerateReference()
    {
        return Ok(Service.GenerateReference());
    }

    [HttpGet("existBy")]
    public virtual async Task<IActionResult> ExistBy(string param)
    {
        return Ok((await Service.Get()).MarshelToDescriptorList(param));
    }

    [HttpGet("descriptor")]
    public virtual async Task<IActionResult> Descriptor(string param)
    {
        return Ok((await Service.Get()).MarshelToDescriptorList(param));
    }


    [HttpGet("{id}")]
    public virtual async Task<IActionResult> Get(Guid id)
    {
        return Ok((await Service.GetOne(id, IncludesProperties)).Marshel(WantedProperties, UnwantedProperties));
    }


    [HttpPost]
    public virtual async Task<IActionResult> Add([FromBody] TEntity entity)
    {
        return Ok((await Service.Add(entity, IncludesProperties)).Marshel(WantedProperties, UnwantedProperties));
    }


    [HttpPost("add-range")]
    public virtual async Task<IActionResult> AddRange([FromBody] List<TEntity> entities)
    {
        if (entities == null || !entities.Any())
            return BadRequest("Aucune entité reçue.");

        var result = await Service.AddRange(entities, IncludesProperties);

        return Ok(result.MarshelList(WantedProperties, UnwantedProperties));
    }


    [HttpPut]
    public virtual async Task<IActionResult> Update([FromBody] TEntity entity, string propertiesToCheck = "")
    {
        return Ok((await Service.Update(entity, IncludesProperties)).Marshel(WantedProperties, UnwantedProperties));
    }

    [HttpDelete]
    public virtual async Task<IActionResult> Delete(Guid id)
    {
        return Ok(await Service.Delete(id));
    }

    [HttpOptions]
    public virtual IActionResult Options()
    {
        return Ok();
    }
}
