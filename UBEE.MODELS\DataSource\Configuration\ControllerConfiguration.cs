﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class ControllerConfiguration : IEntityTypeConfiguration<Controller>
{
    public void Configure(EntityTypeBuilder<Controller> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasMany(s => s.Transactions)
            .WithOne(s => s.Controller)
            .HasForeignKey(s => s.IdController);

        builder.HasMany(s => s.ControllerServerControllers)
             .WithOne(s => s.Controller)
             .HasForeignKey(s => s.IdController);
    }
}