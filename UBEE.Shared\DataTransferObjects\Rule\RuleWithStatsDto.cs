﻿namespace UBEE.MODELS.DataSource.DTOs
{
    public class RuleWithStatsDto
    {
        public Guid RuleId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? LastUpdatedAt { get; set; } // Nullable
        public string LastUpdatedBy { get; set; }
        public DateTime? DeletedAt { get; set; } // Nullable
        public string DeletedBy { get; set; }
        public bool Enabled { get; set; }
        public int Priority { get; set; }
        public string RawData { get; set; }
        public int TotalTransactions { get; set; }
        public int TotalClients { get; set; }
        public int TotalSites { get; set; }
        public int TotalLocals { get; set; }
        public DateTime? LastTriggered { get; set; } // Nullable
        public int TotalApplications { get; set; }
    }
}