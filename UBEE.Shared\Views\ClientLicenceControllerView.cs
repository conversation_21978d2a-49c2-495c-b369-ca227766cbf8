﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UBEE.Shared.Views;

public class ClientLicenceControllerView
{
    // Client
    public Guid? ClientId { get; set; }
    public string? ClientName { get; set; }

    // Licence
    public Guid? LicenceId { get; set; }
    public string? LicenceName { get; set; }

    // Controller Serveur
    public Guid? ControllerServeurId { get; set; }
    public string? ControllerServeurName { get; set; }

    // Controller
    public Guid? ControllerId { get; set; }
    public string? ControllerName { get; set; }
    public string? ControllerModel { get; set; }
    public string? ControllerSerialNumber { get; set; }
    public string? ControllerMacAddress { get; set; }
    public string? ControllerIpAddress { get; set; }
    public DateTime? ControllerLastConnection { get; set; }
    public string? ControllerState { get; set; }
    public string? ControllerBaseTopic { get; set; }
    public DateTime? ControllerInstallationDate { get; set; }
}
