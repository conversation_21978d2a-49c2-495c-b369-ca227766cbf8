﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class VariablesConfiguration : IEntityTypeConfiguration<Variables>
{
    public void Configure(EntityTypeBuilder<Variables> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.TypeCapteur)
            .WithMany(s => s.Variables)
            .HasForeignKey(s => s.IdTypeCapteur);
    }
}
