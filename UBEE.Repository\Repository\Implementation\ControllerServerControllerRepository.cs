﻿using UBEE.Contracts.Repository.Repositories;
using UBEE.MODELS.DataSource;
using UBEE.MODELS.DataSource.Models;
using UBEE.Repository.Repository.Base;

namespace UBEE.Repository.Repository.Implementation;

public class ControllerServerControllerRepository : GenericRepository<ControllerServerController>, IControllerServerControllerRepository
{
    public ControllerServerControllerRepository(UBEEContext context) : base(context)
    {
    }
}
