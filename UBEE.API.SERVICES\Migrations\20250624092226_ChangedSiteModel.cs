﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class ChangedSiteModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_IdClient",
                table: "Site");

            migrationBuilder.RenameColumn(
                name: "Ville",
                table: "Site",
                newName: "Street");

            migrationBuilder.RenameColumn(
                name: "TelephoneSurSite",
                table: "Site",
                newName: "PhoneNumber");

            migrationBuilder.RenameColumn(
                name: "Rue",
                table: "Site",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "Pays",
                table: "Site",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "Nom",
                table: "Site",
                newName: "Contact");

            migrationBuilder.RenameColumn(
                name: "IsEnabled",
                table: "Site",
                newName: "IsActif");

            migrationBuilder.RenameColumn(
                name: "Images",
                table: "Site",
                newName: "Image");

            migrationBuilder.RenameColumn(
                name: "IdClient",
                table: "Site",
                newName: "ClientId");

            migrationBuilder.RenameColumn(
                name: "ContactDeSite",
                table: "Site",
                newName: "City");

            migrationBuilder.RenameIndex(
                name: "IX_Site_IdClient",
                table: "Site",
                newName: "IX_Site_ClientId");

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site",
                column: "ClientId",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Site_Client_ClientId",
                table: "Site");

            migrationBuilder.RenameColumn(
                name: "Street",
                table: "Site",
                newName: "Ville");

            migrationBuilder.RenameColumn(
                name: "PhoneNumber",
                table: "Site",
                newName: "TelephoneSurSite");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "Site",
                newName: "Rue");

            migrationBuilder.RenameColumn(
                name: "IsActif",
                table: "Site",
                newName: "IsEnabled");

            migrationBuilder.RenameColumn(
                name: "Image",
                table: "Site",
                newName: "Images");

            migrationBuilder.RenameColumn(
                name: "Country",
                table: "Site",
                newName: "Pays");

            migrationBuilder.RenameColumn(
                name: "Contact",
                table: "Site",
                newName: "Nom");

            migrationBuilder.RenameColumn(
                name: "ClientId",
                table: "Site",
                newName: "IdClient");

            migrationBuilder.RenameColumn(
                name: "City",
                table: "Site",
                newName: "ContactDeSite");

            migrationBuilder.RenameIndex(
                name: "IX_Site_ClientId",
                table: "Site",
                newName: "IX_Site_IdClient");

            migrationBuilder.AddForeignKey(
                name: "FK_Site_Client_IdClient",
                table: "Site",
                column: "IdClient",
                principalTable: "Client",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
