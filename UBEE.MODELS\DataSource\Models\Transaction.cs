﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Transaction: AuditEntity
{
    public bool? InControl { get; set; }

    public Guid? IdCapteur { get; set; }

    public Capteur? Capteur { get; set; }

    public Guid? IdController { get; set; }

    public Controller? Controller { get; set; }

    public Guid? IdLocal { get; set; }

    public Local? Local { get; set; }

    public List<Log>? Logs { get; set; }

    public List<RuleTransaction>? RuleTransactions { get; set; }

}