﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using UBEE.Contracts.Repository;
using UBEE.Contracts.Repository.Base;
using UBEE.MODELS.Common.Models;
using UBEE.Service.Contracts;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Service;

public class BaseService<TEntity> : IBaseService<TEntity> where TEntity : BaseClass
{
    protected readonly IRepositoryManager UnitOfWork;
    protected IGenericRepository<TEntity> Repository;
    protected string Prefix;

    public BaseService(IRepositoryManager unitOfWork)
    {
        UnitOfWork = unitOfWork;
        Repository = UnitOfWork.GetRepository<TEntity>();
    }

  
    public async Task<IEnumerable<TEntity>> GetAll()
    {
        return await Repository.Get();
    }

    public async Task<IEnumerable<TEntity>> Get(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, string includeProperties = "", int skip = 0,
        int take = 0)
    {
        return await Repository.Get(filter, orderBy, includeProperties, skip, take);
    }

    public async Task<TEntity> GetById(Guid id)
    {
        return await Repository.GetById(id);
    }

    public async Task<TEntity> GetFirstOrDefault()
    {
        return await Repository.GetFirstOrDefault();
    }

    public async Task<int> Count()
    {
        return await Repository.Count();
    }

    public async Task<TEntity> GetByChildId(Guid id, string includeProperties, string entityName)
    {
        return await Repository.GetByChildId(id, includeProperties, entityName);
    }

    public async Task<IEnumerable<TEntity>> GetAllByChildId(Guid id, string entityName, string orderBy = "", bool orderDesc = false, string includeProperties = "")
    {
        var entities = await Repository.GetAllByChildId(id, entityName, orderBy, orderDesc, includeProperties);

        return entities;
    }

    public async Task<TEntity> GetOne(Guid id, string includeProperties = "")
    {
        return await Repository.GetOne(id, includeProperties);
    }

    public virtual async Task<TEntity> Add(TEntity entity, string includeProperties = "")
    {
        var record = await Repository.Insert(entity);
        UnitOfWork.Save();

        record = await Repository.GetOne(record.Id, includeProperties);
        return record;
    }

    public virtual async Task<TEntity> Update(TEntity entity, string propertiesToCheck = "")
    {
        TEntity record = await Repository.Update(entity, propertiesToCheck);
        await UnitOfWork.SaveAsync();
        return record;
    }

    public virtual TEntity UpdateValues(TEntity entity)
    {
        TEntity record = Repository.UpdateValues(entity);
        UnitOfWork.Save();
        return record;
    }

    public virtual TEntity Update(TEntity entity)
    {
        TEntity record = Repository.UpdateValues(entity);
        UnitOfWork.Save();
        return record;
    }

    public virtual TEntity Delete(TEntity entity)
    {
        TEntity record = Repository.Delete(entity);
        UnitOfWork.Save();
        return record;
    }

    public virtual async Task<bool> Delete(Guid id)
    {
        bool isDeleted = await Repository.Delete(id);

        return isDeleted && UnitOfWork.Save() != -1;
    }

    public virtual async Task<Pager<TEntity>> GetPages(Lister<TEntity> lister, string includeProperties, IQueryable<TEntity> query = null)
    {
        return await Repository.GetPages(lister, includeProperties, query);
    }

    public virtual string GenerateReference()
    {
        if (string.IsNullOrEmpty(Prefix))
            return Repository.GenerateReference();

        return Repository.GenerateReference(Prefix);
    }

    public virtual TEntity CheckIfExistByProperty(string propertyName, object value)
    {
        return Repository.CheckIfExistByProperty(propertyName, value);
    }

    public async Task<IEnumerable<TEntity>> AddRange(List<TEntity> entities, string includeProperties = "")
    {
        var inserted = await Repository.InsertRange(entities);
        UnitOfWork.Save();

        // Optionnel : si tu veux recharger les entités avec navigation properties
        if (!string.IsNullOrEmpty(includeProperties))
        {
            var ids = inserted.Select(e => e.Id).ToList();
            return await Repository.Get(e => ids.Contains(e.Id), null, includeProperties);
        }

        return inserted;
    }
}