﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class RulesConfiguration : IEntityTypeConfiguration<Rules>
{
    public void Configure(EntityTypeBuilder<Rules> builder)
    {
        builder.HasKey(s => s.Id);

        builder.HasMany(s => s.ControllerServerRules)
            .WithOne(s => s.Rules)
            .HasForeignKey(s => s.IdRules);
    }
}
