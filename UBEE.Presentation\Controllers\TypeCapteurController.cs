﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/type-capteur")]
[ApiController]
public class TypeCapteurController : BaseController<TypeCapteur>
{
    private readonly ITypeCapteurService _service;
    public TypeCapteurController(ITypeCapteurService baseService) : base(baseService)
    {
        this._service = baseService as ITypeCapteurService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}
