using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.Shared.Views;
using UBEE.Shared.DataTransferObjects.Dto;

namespace UBEE.Presentation.Controllers;

[Route("api/tag-assignment-counts")]
[ApiController]
public class TagAssignmentCountsViewController : ControllerBase
{
    private readonly UBEEContext _context;

    public TagAssignmentCountsViewController(UBEEContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var results = await _context.vwTagAssignmentCountsPerTag.ToListAsync();
        return Ok(results);
    }

    [HttpGet("tag/{tagId}")]
    public async Task<IActionResult> GetByTagId(Guid tagId)
    {
        var result = await _context.vwTagAssignmentCountsPerTag
            .FirstOrDefaultAsync(v => v.TagId == tagId);
        
        if (result == null)
        {
            return NotFound($"No tag assignment counts found for tag ID: {tagId}");
        }
        
        return Ok(result);
    }

    [HttpGet("top-tags/{count}")]
    public async Task<IActionResult> GetTopTagsByAssignments(int count = 10)
    {
        var results = await _context.vwTagAssignmentCountsPerTag
            .OrderByDescending(v => v.TotalAssignments)
            .Take(count)
            .ToListAsync();
        return Ok(results);
    }

    [HttpGet("tags-with-clients")]
    public async Task<IActionResult> GetTagsWithClients()
    {
        var results = await _context.vwTagAssignmentCountsPerTag
            .Where(v => v.ClientCount > 0)
            .OrderByDescending(v => v.ClientCount)
            .ToListAsync();
        return Ok(results);
    }

    [HttpGet("tags-with-sites")]
    public async Task<IActionResult> GetTagsWithSites()
    {
        var results = await _context.vwTagAssignmentCountsPerTag
            .Where(v => v.SiteCount > 0)
            .OrderByDescending(v => v.SiteCount)
            .ToListAsync();
        return Ok(results);
    }

    [HttpGet("tags-with-locals")]
    public async Task<IActionResult> GetTagsWithLocals()
    {
        var results = await _context.vwTagAssignmentCountsPerTag
            .Where(v => v.LocalCount > 0)
            .OrderByDescending(v => v.LocalCount)
            .ToListAsync();
        return Ok(results);
    }

    [HttpPost("search")]
    public async Task<IActionResult> Search([FromBody] PaginationDto payload)
    {
        int pageSize = payload.PageSize ?? 10;
        int pageNumber = payload.PageNumber ?? 1;
        int skip = (pageNumber - 1) * pageSize;
        string search = payload.SearchTerm?.ToLower() ?? "";

        var query = _context.vwTagAssignmentCountsPerTag.AsQueryable();

        // Apply search filter if provided
        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(v => 
                (v.TagName != null && v.TagName.ToLower().Contains(search)) ||
                (v.TagId != null && v.TagId.ToString().ToLower().Contains(search))
            );
        }

        var totalCount = await query.CountAsync();
        var results = await query
            .OrderByDescending(v => v.TotalAssignments)
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();

        return Ok(new
        {
            Data = results,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        });
    }

    [HttpGet("summary")]
    public async Task<IActionResult> GetSummary()
    {
        var summary = await _context.vwTagAssignmentCountsPerTag
            .GroupBy(v => 1)
            .Select(g => new
            {
                TotalTags = g.Count(),
                TotalAssignments = g.Sum(v => v.TotalAssignments ?? 0),
                TotalClientAssignments = g.Sum(v => v.ClientCount ?? 0),
                TotalSiteAssignments = g.Sum(v => v.SiteCount ?? 0),
                TotalLocalAssignments = g.Sum(v => v.LocalCount ?? 0),
                AverageAssignmentsPerTag = g.Average(v => v.TotalAssignments ?? 0)
            })
            .FirstOrDefaultAsync();

        return Ok(summary);
    }
}
