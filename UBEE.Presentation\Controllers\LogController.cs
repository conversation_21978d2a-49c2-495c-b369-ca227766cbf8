﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace UBEE.Presentation.Controllers;

[Route("api/log")]
[ApiController]
public class LogController : BaseController<Log>
{
    private readonly ILogService _service;

    public LogController(ILogService baseService) : base(baseService)
    {
        this._service = baseService as ILogService;
        this.UnwantedProperties += "";
        IncludesProperties = "Transaction";
    }

    /// <summary>
    /// Generates logs when a rule is applied, based on the rule's related capteurs
    /// </summary>
    /// <param name="request">The request containing rule application details</param>
    /// <returns>The number of logs created</returns>
    [HttpPost("generate-from-rule")]
    public async Task<IActionResult> GenerateLogsFromRule([FromBody] GenerateLogsFromRuleRequest request)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Call the service method
            var logsCreated = await _service.GenerateLogsFromRuleAsync(
                request.IdRule,
                request.IdLocal,
                request.IdController,
                request.RawDataJson
            );

            // Return success response
            var response = new GenerateLogsResponse
            {
                LogsCreated = logsCreated,
                Success = true,
                Message = logsCreated > 0
                    ? $"Successfully created {logsCreated} log(s) for rule application."
                    : "No logs were created. No matching transactions found for the rule topics."
            };

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest($"Invalid input: {ex.Message}");
        }
        catch (InvalidOperationException ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError,
                $"Error processing rule: {ex.Message}");
        }
        catch (Exception ex)
        {
            // Log the exception here if you have logging configured
            return StatusCode(StatusCodes.Status500InternalServerError,
                "An unexpected error occurred while generating logs from rule.");
        }
    }
}

/// <summary>
/// Request model for generating logs from rule application
/// </summary>
public class GenerateLogsFromRuleRequest
{
    /// <summary>
    /// The ID of the rule being applied
    /// </summary>
    [Required]
    public Guid IdRule { get; set; }

    /// <summary>
    /// The local ID where the rule applies
    /// </summary>
    [Required]
    public Guid IdLocal { get; set; }

    /// <summary>
    /// The controller ID where the rule applies
    /// </summary>
    [Required]
    public Guid IdController { get; set; }

    /// <summary>
    /// The JSON string containing the rule data with topic_pattern and actions
    /// </summary>
    /// <example>
    /// {
    ///   "topic_pattern": ["zigbee2mqtt/sensor_door_entry"],
    ///   "actions": [
    ///     { "topic": "zigbee2mqtt/roller_shade/set" }
    ///   ]
    /// }
    /// </example>
    [Required]
    [MinLength(1, ErrorMessage = "RawDataJson cannot be empty")]
    public string RawDataJson { get; set; } = string.Empty;
}

/// <summary>
/// Response model for generate logs from rule operation
/// </summary>
public class GenerateLogsResponse
{
    /// <summary>
    /// Number of logs created
    /// </summary>
    public int LogsCreated { get; set; }

    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Descriptive message about the operation result
    /// </summary>
    public string Message { get; set; } = string.Empty;
}