﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class SubscribedOptionsConfiguration : IEntityTypeConfiguration<SubscribedOptions>
{
    public void Configure(EntityTypeBuilder<SubscribedOptions> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasOne(s => s.Option)
            .WithMany()
            .HasForeignKey(s => s.OptionId);

        builder.HasOne(s => s.Subscription)
            .WithMany(s => s.SubscribedOptions)
            .HasForeignKey(s => s.SubscriptionId);
    }
}