﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class ControllerServeur : AuditEntity
{
    public string Name { get; set; } // Nom

    public int? MaxControllers { get; set; } // Nombre maximal de contrôleurs

    public int? MaxSensors { get; set; } // Nombre maximal de capteurs

    public string? GeographicZone { get; set; } // Zone géographique

    public string? CommercialCondition { get; set; } // Condition commerciale

    public string? TriggerType { get; set; } // Type de déclencheur

    public string? ActionType { get; set; } // Type d’action

    public string? EventType { get; set; } // Type d’événement

    public string? Status { get; set; } // Statut

    public Guid? SubscriptionId { get; set; }

    public Subscription Subscription { get; set; }
    
    public List<ControllerServerController> ControllerServerControllers { get; set; }

    public List<ControllerServerRule> ControllerServerRules { get; set; }
}
