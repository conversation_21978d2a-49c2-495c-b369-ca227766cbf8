using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class TagAssignmentConfiguration : IEntityTypeConfiguration<TagAssignment>
{
    public void Configure(EntityTypeBuilder<TagAssignment> builder)
    {
        builder.HasKey(s => s.Id);

        builder.HasOne(s => s.Tag)
            .WithMany()
            .HasForeignKey(s => s.IdTag)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(s => s.TargetType)
            .HasConversion<string>();
    }
}