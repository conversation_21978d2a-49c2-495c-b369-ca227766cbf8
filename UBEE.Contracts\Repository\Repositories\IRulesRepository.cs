﻿using UBEE.MODELS.DataSource.DTOs;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Contracts.Repository;

public interface IRulesRepository
{
    /// <summary>
    /// Gets paginated rules with database-level pagination and filtering
    /// </summary>
    Task<Pager<RuleComprehensiveDto>> GetRulesComprehensiveAsync(Lister<RuleComprehensiveDto> lister);

    /// <summary>
    /// Searches rules with database-level pagination and filtering
    /// </summary>
    Task<Pager<RuleComprehensiveDto>> SearchRulesAsync(RulesLister rulesLister);

    /// <summary>
    /// Gets a single rule by ID with comprehensive data
    /// </summary>
    Task<RuleComprehensiveDto?> GetRuleComprehensiveByIdAsync(Guid ruleId);

    /// <summary>
    /// Gets rule client hierarchy data
    /// </summary>
    Task<IEnumerable<RuleClientHierarchyDto>> GetRuleClientHierarchyAsync(Guid ruleId);

    /// <summary>
    /// Gets rule tags
    /// </summary>
    Task<IEnumerable<RuleWithTagDto>> GetRuleTagsAsync(Guid ruleId);


    /// <summary>
    /// Gets transaction details for a rule
    /// </summary>
    Task<IEnumerable<RuleTransactionDetailDto>> GetRuleTransactionDetailsAsync(Guid ruleId);

    /// <summary>
    /// Gets rule execution chart data grouped by date for chart visualization
    /// </summary>
    Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionChartDataAsync(Guid ruleId, int days = 30);

    /// <summary>
    /// Gets hourly execution data for a specific date for detailed chart view
    /// </summary>
    Task<IEnumerable<RuleExecutionChartDto>> GetRuleExecutionHourlyDataAsync(Guid ruleId, DateTime date);

    /// <summary>
    /// Gets recent individual executions for debugging/detail view
    /// </summary>
    Task<IEnumerable<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, int limit = 50);

    /// <summary>
    /// Gets paginated recent individual executions for debugging/detail view
    /// </summary>
    Task<Pager<RuleExecutionSimpleDto>> GetRecentRuleExecutionsAsync(Guid ruleId, Lister<RuleExecutionSimpleDto> lister);

}