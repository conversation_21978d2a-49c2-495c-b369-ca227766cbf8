﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class ChangedTypeCapteurAndVariables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NomTechnique",
                table: "Variables");

            migrationBuilder.RenameColumn(
                name: "ComponentType",
                table: "TypeCapteur",
                newName: "Capabilities");

            migrationBuilder.AddColumn<bool>(
                name: "Readable",
                table: "Variables",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Readable",
                table: "Variables");

            migrationBuilder.RenameColumn(
                name: "Capabilities",
                table: "TypeCapteur",
                newName: "ComponentType");

            migrationBuilder.AddColumn<string>(
                name: "NomTechnique",
                table: "Variables",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}
