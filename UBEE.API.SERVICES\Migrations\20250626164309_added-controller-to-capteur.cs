﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedcontrollertocapteur : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ControllerId",
                table: "Capteur",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Capteur_ControllerId",
                table: "Capteur",
                column: "ControllerId");

            migrationBuilder.AddForeignKey(
                name: "FK_Capteur_Controller_ControllerId",
                table: "Capteur",
                column: "ControllerId",
                principalTable: "Controller",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Capteur_Controller_ControllerId",
                table: "Capteur");

            migrationBuilder.DropIndex(
                name: "IX_Capteur_ControllerId",
                table: "Capteur");

            migrationBuilder.DropColumn(
                name: "ControllerId",
                table: "Capteur");
        }
    }
}
