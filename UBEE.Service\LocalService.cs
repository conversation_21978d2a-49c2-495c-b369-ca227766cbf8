﻿using UBEE.Contracts.Repository;
using UBEE.Contracts.Repository.Repositories;
using UBEE.MODELS.DataSource.Models;
using UBEE.Service.Contracts;
using UBEE.Shared.GenericListModel.Filtering;

namespace UBEE.Service;

public sealed class LocalService : BaseService<Local>, ILocalService
{
    private ILocalRepository Repository;
    public LocalService(IRepositoryManager unitOfWork, ILocalRepository repository) : base(unitOfWork)
    {
        Repository = repository as ILocalRepository
            ?? throw new InvalidCastException("Repository is not of type ILocalRepository");
    }

    public async Task<string> GetArchitecture(object id)
    {
        return await Repository.GetArchitecture(id);
    }

    public override Task<Pager<Local>> GetPages(Lister<Local> lister, string includeProperties, IQueryable<Local> query = null)
    {
        return base.GetPages(lister, includeProperties, query);
    }
}
