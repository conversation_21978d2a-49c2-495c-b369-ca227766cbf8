﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UBEE.API.SERVICES.Migrations
{
    /// <inheritdoc />
    public partial class addedsite : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            //migrationBuilder.AddColumn<string>(
            //    name: "AddressComplement",
            //    table: "Site",
            //    type: "nvarchar(max)",
            //    nullable: false,
            //    defaultValue: "");

            //migrationBuilder.AddColumn<string>(
            //    name: "Adress",
            //    table: "Site",
            //    type: "nvarchar(max)",
            //    nullable: false,
            //    defaultValue: "");

            //migrationBuilder.AddColumn<string>(
            //    name: "Email",
            //    table: "Site",
            //    type: "nvarchar(max)",
            //    nullable: false,
            //    defaultValue: "");

            //migrationBuilder.AddColumn<string>(
            //    name: "Grade",
            //    table: "Site",
            //    type: "nvarchar(max)",
            //    nullable: false,
            //    defaultValue: "");

            //migrationBuilder.AddColumn<string>(
            //    name: "Latitude",
            //    table: "Site",
            //    type: "nvarchar(max)",
            //    nullable: false,
            //    defaultValue: "");

            //migrationBuilder.AddColumn<string>(
            //    name: "Longitude",
            //    table: "Site",
            //    type: "nvarchar(max)",
            //    nullable: false,
            //    defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AddressComplement",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Adress",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Grade",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "Longitude",
                table: "Site");

            migrationBuilder.RenameColumn(
                name: "Surface",
                table: "Site",
                newName: "Street");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "Site",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "Manager",
                table: "Site",
                newName: "City");

            migrationBuilder.AddColumn<bool>(
                name: "IsActif",
                table: "Site",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
