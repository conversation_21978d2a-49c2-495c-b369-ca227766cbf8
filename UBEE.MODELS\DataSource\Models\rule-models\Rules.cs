﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class Rules : AuditEntity
{
    public string? Summary { get; set; }
    public string RawData { get; set; }

    public bool Enabled { get; set; }

    public int Priority { get; set; }

    public List<ControllerServerRule> ControllerServerRules { get; set; }

    public List<RuleTransaction> RuleTransactions { get; set; }

    public List<RuleTag> RuleTags { get; set; }
}   
