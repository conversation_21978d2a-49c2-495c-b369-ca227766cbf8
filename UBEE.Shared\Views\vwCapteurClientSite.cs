﻿namespace UBEE.Shared.Views;

public class vwCapteurClientSite
{
    public Guid? SiteId { get; set; }
    public string? SiteName { get; set; }
    public Guid? ClientId { get; set; }
    public string? ClientName { get; set; }
    public Guid? LocalId { get; set; }
    public string? LocalName { get; set; }
    public Guid? Id { get; set; }
    public string? DisplayName { get; set; }
    public string? FriendlyName { get; set; }
    public string? Model { get; set; }
    public string? Brand { get; set; }
    public string? RowData { get; set; }
    public DateTime? LastSeen { get; set; }
    public bool? InControl { get; set; }
    public string? Topic { get; set; }
    public string? State { get; set; }
    public Guid? ControllerId { get; set; }
    public string? HostName { get; set; }
    public string? BaseTopic { get; set; }
    public Guid? CSId { get; set; }
    public string? CSName { get; set; }
}