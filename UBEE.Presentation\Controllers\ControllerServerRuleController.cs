﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/controller-serveur-rule")]
[ApiController]
public class ControllerServerRuleController : BaseController<ControllerServerRule>
{
    private readonly IControllerServerRuleService _service;
    public ControllerServerRuleController(IControllerServerRuleService baseService) : base(baseService)
    {
        this._service = baseService as IControllerServerRuleService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}

