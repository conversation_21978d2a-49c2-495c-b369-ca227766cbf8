﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/type-local")]
[ApiController]
public class TypeLocalController : BaseController<TypeLocal>
{
    private readonly ITypeLocalService _service;
    public TypeLocalController(ITypeLocalService baseService) : base(baseService)
    {
        this._service = baseService as ITypeLocalService;
        this.UnwantedProperties += "";
        IncludesProperties ="";
    }
}

