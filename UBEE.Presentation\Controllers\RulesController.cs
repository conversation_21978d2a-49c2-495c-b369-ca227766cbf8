﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.MODELS.DataSource.DTOs;
using UBEE.Shared.GenericListModel.Filtering;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;
using System.Text.Json;

namespace UBEE.Presentation.Controllers;

[Route("api/rule")]
[ApiController]
public class RulesController : BaseController<Rules>
{
    private readonly IRulesService _service;

    public RulesController(IRulesService baseService) : base(baseService)
    {
        this._service = baseService as IRulesService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }

    /// <summary>
    /// Get all rules with comprehensive statistics and pagination using query parameters
    /// Available sort columns: Priority, RuleCreatedAt, RuleLastUpdatedAt, Status, TotalApplications, LastTriggered
    /// Available filter format: JSON array of filter objects
    /// Example: [{"column":"status","value":"active","op":"eq","andOr":"AND"}]
    /// </summary>
    [HttpGet("comprehensive")]
    public async Task<ActionResult> GetRulesComprehensive(
     [FromQuery] int page = 1,
     [FromQuery] int pageSize = 10,
     [FromQuery] string? sortBy = "Priority",
     [FromQuery] string? sortDirection = "ASC",
     [FromQuery] string? filters = null)
    {
        try
        {
            // Log incoming parameters
            Console.WriteLine($"API Request - Page: {page}, PageSize: {pageSize}, SortBy: {sortBy}, Direction: {sortDirection}");
            Console.WriteLine($"Filters: {filters ?? "null"}");

            // Validate parameters
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            var filterParams = ParseFilters(filters);
            var sortParams = ParseSortParameters(sortBy, sortDirection);

            Console.WriteLine($"Parsed Filters Count: {filterParams?.Count ?? 0}");
            Console.WriteLine($"Parsed Sort Params Count: {sortParams?.Count ?? 0}");

            var lister = new Lister<RuleComprehensiveDto>
            {
                Pagination = new Pagination
                {
                    CurrentPage = page,
                    PageSize = pageSize
                },
                SortParams = sortParams,
                FilterParams = filterParams
            };

            Console.WriteLine("Calling service...");
            var result = await _service.GetRulesComprehensiveAsync(lister);
            Console.WriteLine($"Service returned {result.Content.Count()} items");

            AddPaginationHeaders(result.Lister.Pagination);

            return Ok(result);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"FULL ERROR: {ex}");
            Console.WriteLine($"Inner Exception: {ex.InnerException?.Message}");
            return StatusCode(500, $"Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Advanced endpoint for complex filtering and sorting with POST body
    /// </summary>
    [HttpPost("comprehensive/advanced")]
    public async Task<ActionResult> GetRulesComprehensiveAdvanced([FromBody] AdvancedRulesRequest request)
    {
        try
        {
            var lister = new Lister<RuleComprehensiveDto>
            {
                Pagination = new Pagination
                {
                    CurrentPage = Math.Max(1, request.Page ?? 1),
                    PageSize = Math.Max(1, Math.Min(100, request.PageSize ?? 10))
                },
                SortParams = request.SortParams ?? new List<Sorting>
                {
                    new() { Column = "Priority", Sort = "ASC" }
                },
                FilterParams = request.FilterParams ?? new List<Filtering.WhereParams>()
            };

            var result = await _service.GetRulesComprehensiveAsync(lister);

            AddPaginationHeaders(result.Lister.Pagination);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Get all rules with comprehensive statistics (no pagination for backward compatibility)
    /// </summary>
    [HttpGet("comprehensive/all")]
    public async Task<ActionResult> GetAllRulesComprehensive()
    {
        try
        {
            var rules = await _service.GetRulesComprehensiveAsync();
            return Ok(rules);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Search rules by name or tags with pagination using query parameters
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult> SearchRules(
        [FromQuery] string searchTerm,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? sortBy = "Priority",
        [FromQuery] string? sortDirection = "ASC",
        [FromQuery] bool includeInactive = false,
        [FromQuery] string? filters = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest("Search term is required");

            var filterParams = ParseFilters(filters);
            var sortParams = ParseSortParameters(sortBy, sortDirection);

            var rulesLister = new RulesLister
            {
                SearchTerm = searchTerm,
                IncludeInactive = includeInactive,
                Pagination = new Pagination
                {
                    CurrentPage = Math.Max(1, page),
                    PageSize = Math.Max(1, Math.Min(100, pageSize))
                },
                SortParams = sortParams,
                FilterParams = filterParams
            };

            var result = await _service.SearchRulesAsync(rulesLister);

            AddPaginationHeaders(result.Lister.Pagination);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error searching rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Advanced search endpoint with POST for complex filters and search criteria
    /// </summary>
    [HttpPost("search/advanced")]
    public async Task<ActionResult> SearchRulesAdvanced([FromBody] AdvancedSearchRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.SearchTerm))
                return BadRequest("Search term is required");

            var rulesLister = new RulesLister
            {
                SearchTerm = request.SearchTerm,
                IncludeInactive = request.IncludeInactive ?? false,
                Pagination = new Pagination
                {
                    CurrentPage = Math.Max(1, request.Page ?? 1),
                    PageSize = Math.Max(1, Math.Min(100, request.PageSize ?? 10))
                },
                SortParams = request.SortParams ?? new List<Sorting>
                {
                    new() { Column = "Priority", Sort = "ASC" }
                },
                FilterParams = request.FilterParams ?? new List<Filtering.WhereParams>()
            };

            var result = await _service.SearchRulesAsync(rulesLister);

            AddPaginationHeaders(result.Lister.Pagination);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error searching rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Search rules by name or tags (no pagination for backward compatibility)
    /// </summary>
    [HttpGet("search/all")]
    public async Task<ActionResult> SearchAllRules([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest("Search term is required");

            var rules = await _service.SearchRulesAsync(searchTerm);
            return Ok(rules);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error searching rules: {ex.Message}");
        }
    }

    /// <summary>
    /// Get a specific rule with comprehensive details
    /// </summary>
    [HttpGet("{ruleId:guid}/comprehensive")]
    public async Task<ActionResult> GetRuleComprehensive(Guid ruleId)
    {
        try
        {
            var rule = await _service.GetRuleComprehensiveByIdAsync(ruleId);
            if (rule == null)
                return NotFound($"Rule with ID {ruleId} not found");

            return Ok(rule);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule: {ex.Message}");
        }
    }

    /// <summary>
    /// Get the client hierarchy for a specific rule (for expanded details)
    /// </summary>
    [HttpGet("{ruleId:guid}/hierarchy")]
    public async Task<ActionResult> GetRuleClientHierarchy(Guid ruleId)
    {
        try
        {
            var hierarchy = await _service.GetRuleClientHierarchyAsync(ruleId);
            return Ok(hierarchy);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule hierarchy: {ex.Message}");
        }
    }

    /// <summary>
    /// Get tags for a specific rule
    /// </summary>
    [HttpGet("{ruleId:guid}/tags")]
    public async Task<ActionResult> GetRuleTags(Guid ruleId)
    {
        try
        {
            var tags = await _service.GetRuleTagsAsync(ruleId);
            return Ok(tags);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule tags: {ex.Message}");
        }
    }

    /// <summary>
    /// Get transaction details for a specific rule
    /// </summary>
    [HttpGet("{ruleId:guid}/transaction-details")]
    public async Task<ActionResult> GetRuleTransactionDetails(Guid ruleId)
    {
        try
        {
            var transactionDetails = await _service.GetRuleTransactionDetailsAsync(ruleId);
            return Ok(transactionDetails);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving rule transaction details: {ex.Message}");
        }
    }

    /// <summary>
    /// Get bulk hierarchy data for multiple rules (optimization for frontend)
    /// </summary>
    [HttpPost("hierarchy/bulk")]
    public async Task<ActionResult> GetBulkRuleHierarchy([FromBody] List<Guid> ruleIds)
    {
        try
        {
            if (ruleIds == null || !ruleIds.Any())
                return BadRequest("Rule IDs are required");

            if (ruleIds.Count > 100) // Prevent abuse
                return BadRequest("Too many rule IDs. Maximum 100 allowed.");

            var tasks = ruleIds.Select(async ruleId => new
            {
                RuleId = ruleId,
                Hierarchy = await _service.GetRuleClientHierarchyAsync(ruleId)
            });

            var results = await Task.WhenAll(tasks);
            return Ok(results);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving bulk hierarchy: {ex.Message}");
        }
    }

    // NEW: Rule Execution Endpoints Based on Actual Log Data

    /// <summary>
    /// Get actual rule execution logs with pagination
    /// Available sort columns: ExecutionTimestamp, IsSuccess, Status, ControllerName, LocalName
    /// Available filter format: JSON array of filter objects
    /// Example: [{"column":"isSuccess","value":"true","op":"eq","andOr":"AND"}]
    /// </summary>
    /// <summary>
    /// Get rule execution chart data grouped by date (for main chart view)
    /// </summary>
    [HttpGet("{ruleId:guid}/execution-chart")]
    public async Task<ActionResult> GetRuleExecutionChart(Guid ruleId, [FromQuery] int days = 30)
    {
        try
        {
            days = Math.Max(1, Math.Min(365, days)); // Limit between 1 and 365 days
            var chartData = await _service.GetRuleExecutionChartDataAsync(ruleId, days);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving execution chart data: {ex.Message}");
        }
    }

    /// <summary>
    /// Get hourly execution data for a specific date (for detailed view)
    /// </summary>
    [HttpGet("{ruleId:guid}/execution-chart/hourly")]
    public async Task<ActionResult> GetRuleExecutionHourlyChart(Guid ruleId, [FromQuery] DateTime date)
    {
        try
        {
            var hourlyData = await _service.GetRuleExecutionHourlyDataAsync(ruleId, date);
            return Ok(hourlyData);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving hourly execution data: {ex.Message}");
        }
    }

    /// <summary>
    /// Get recent individual executions (for debugging/details)
    /// </summary>
    [HttpGet("{ruleId:guid}/recent-executions")]
    public async Task<ActionResult> GetRecentRuleExecutions(Guid ruleId, [FromQuery] int limit = 50)
    {
        try
        {
            limit = Math.Max(1, Math.Min(100, limit));
            var executions = await _service.GetRecentRuleExecutionsAsync(ruleId, limit);
            return Ok(executions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving recent executions: {ex.Message}");
        }
    }
    [HttpGet("{ruleId:guid}/recent-executions/paginated")]
    public async Task<ActionResult> GetRecentRuleExecutionsPaginated(
    Guid ruleId,
    [FromQuery] int page = 1,
    [FromQuery] int pageSize = 20,
    [FromQuery] string? sortBy = "ExecutionTimestamp",
    [FromQuery] string? sortDirection = "DESC",
    [FromQuery] string? filters = null)
    {
        try
        {
            // Validate parameters
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 20;

            var filterParams = ParseExecutionFilters(filters);
            var sortParams = ParseSortParameters(sortBy, sortDirection);

            var lister = new Lister<RuleExecutionSimpleDto>
            {
                Pagination = new Pagination
                {
                    CurrentPage = page,
                    PageSize = pageSize
                },
                SortParams = sortParams,
                FilterParams = filterParams
            };

            var result = await _service.GetRecentRuleExecutionsAsync(ruleId, lister);

            AddPaginationHeaders(result.Lister.Pagination);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving paginated recent executions: {ex.Message}");
        }
    }
    #region Helper Methods
    /// <summary>
    /// Parses execution-specific filter string into WhereParams list
    /// Expected format: JSON array of filter objects
    /// Example: [{"column":"isSuccess","value":"true","op":"eq","andOr":"AND"}]
    /// </summary>
    private List<Filtering.WhereParams> ParseExecutionFilters(string? filtersJson)
    {
        if (string.IsNullOrWhiteSpace(filtersJson))
        {
            return new List<Filtering.WhereParams>();
        }

        try
        {
            var filters = JsonSerializer.Deserialize<List<Filtering.WhereParams>>(filtersJson, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            // Validate execution-specific columns
            var validColumns = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "ExecutionTimestamp", "Timestamp", "CreatedAt",
            "IsSuccess", "Success", "Status",
            "ControllerId", "Controller"
        };

            if (filters != null)
            {
                // Filter out invalid columns and log warnings
                var validFilters = filters.Where(f =>
                    !string.IsNullOrEmpty(f.Column) &&
                    validColumns.Contains(f.Column)).ToList();

                if (validFilters.Count != filters.Count)
                {
                    Console.WriteLine($"Warning: Some invalid filter columns were removed. Valid columns: {string.Join(", ", validColumns)}");
                }

                return validFilters;
            }

            return new List<Filtering.WhereParams>();
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"JSON parsing error for execution filters: {ex.Message}");
            return new List<Filtering.WhereParams>();
        }
    }

    /// <summary>
    /// Adds pagination headers to the response
    /// </summary>
    private void AddPaginationHeaders(Pagination pagination)
    {
        Response.Headers.Add("X-Pagination-CurrentPage", pagination.CurrentPage.ToString());
        Response.Headers.Add("X-Pagination-PageSize", pagination.PageSize.ToString());
        Response.Headers.Add("X-Pagination-TotalItems", pagination.TotalElement.ToString());
        Response.Headers.Add("X-Pagination-TotalPages", pagination.PageCount.ToString());
        Response.Headers.Add("X-Pagination-IsFirst", pagination.IsFirst.ToString());
        Response.Headers.Add("X-Pagination-IsLast", pagination.IsLast.ToString());
    }

    /// <summary>
    /// Parses filter string into WhereParams list
    /// Expected format: JSON array of filter objects
    /// Example: [{"column":"status","value":"active","op":"eq","andOr":"AND"}]
    /// </summary>
    private List<Filtering.WhereParams> ParseFilters(string? filtersJson)
    {
        if (string.IsNullOrWhiteSpace(filtersJson))
        {
            Console.WriteLine("No filters provided");
            return new List<Filtering.WhereParams>();
        }

        try
        {
            Console.WriteLine($"Parsing filters: {filtersJson}");

            var filters = JsonSerializer.Deserialize<List<Filtering.WhereParams>>(filtersJson, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Console.WriteLine($"Successfully parsed {filters?.Count ?? 0} filters");

            // Log each filter for debugging
            if (filters != null)
            {
                foreach (var filter in filters)
                {
                    Console.WriteLine($"Filter: Column={filter.Column}, Value={filter.Value}, Op={filter.Op}, AndOr={filter.AndOr}");
                }
            }

            return filters ?? new List<Filtering.WhereParams>();
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"JSON parsing error: {ex.Message}");
            return new List<Filtering.WhereParams>();
        }
    }

    /// <summary>
    /// Parses sort parameters into Sorting list with support for multiple sorts
    /// </summary>
    private List<Sorting> ParseSortParameters(string? sortBy, string? sortDirection)
    {
        var sortParams = new List<Sorting>();

        if (!string.IsNullOrWhiteSpace(sortBy))
        {
            // Handle comma-separated sort columns
            var sortColumns = sortBy.Split(',', StringSplitOptions.RemoveEmptyEntries);
            var sortDirections = sortDirection?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? new[] { "ASC" };

            for (int i = 0; i < sortColumns.Length; i++)
            {
                var column = sortColumns[i].Trim();
                var direction = i < sortDirections.Length ? sortDirections[i].Trim() : "ASC";

                sortParams.Add(new Sorting
                {
                    Column = column,
                    Sort = direction.ToUpper() == "DESC" ? "DESC" : "ASC"
                });
            }
        }

        // Default sorting if none provided
        if (!sortParams.Any())
        {
            sortParams.Add(new Sorting { Column = "Priority", Sort = "ASC" });
        }

        return sortParams;
    }

    #endregion
}

public class AdvancedExecutionsRequest
{
    public int? Page { get; set; }
    public int? PageSize { get; set; }
    public List<Sorting>? SortParams { get; set; }
    public List<Filtering.WhereParams>? FilterParams { get; set; }
}