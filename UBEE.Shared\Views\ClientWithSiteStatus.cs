using System.ComponentModel.DataAnnotations.Schema;

namespace UBEE.MODELS.DataSource.Models;

public class ClientWithSiteStatus
{
    public Guid ClientId { get; set; }
    public string Name { get; set; }
    public string? ContactEmail { get; set; }
    public string Phone { get; set; }
    public string? Organisation { get; set; }
    public string? ContactAddress { get; set; }
    public string RC { get; set; }
    public string IF { get; set; }
    public string ClientStatus { get; set; }
    public int SiteInactif { get; set; }
    public int SiteActif { get; set; }
    public int SiteEnMaintenance { get; set; }
    public int SiteEnInstallation { get; set; }
}