﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class TypeCapteurConfiguration : IEntityTypeConfiguration<TypeCapteur>
{
    public void Configure(EntityTypeBuilder<TypeCapteur> builder)
    {
        builder.HasKey(s => s.Id);

        builder.HasMany(s => s.Variables)
            .WithOne(s => s.TypeCapteur)
            .HasForeignKey(s => s.IdTypeCapteur);
    }
}