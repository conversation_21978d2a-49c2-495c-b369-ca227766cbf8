﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UBEE.MODELS.DataSource.Models;

namespace UBEE.MODELS.DataSource.Configuration;

public class CapteurConfiguration : IEntityTypeConfiguration<Capteur>
{
    public void Configure(EntityTypeBuilder<Capteur> builder)
    {
        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.HasMany(s => s.Transactions)
            .WithOne(s => s.Capteur)
            .HasForeignKey(s => s.IdCapteur);

        builder.HasMany(s => s.SensorReadings)
            .WithOne(s => s.Capteur)
            .HasForeignKey(s => s.IdCapteur);

        builder.HasOne(s => s.TypeCapteur)
            .WithMany()
            .HasForeignKey(s => s.IdTypeCapteur);
    }
}
