﻿using Microsoft.AspNetCore.Mvc;
using UBEE.MODELS.DataSource.Models;
using UBEE.Presentation.Controllers.Base;
using UBEE.Service.Contracts;

namespace UBEE.Presentation.Controllers;

[Route("api/option")]
[ApiController]
public class OptionController : BaseController<Option>
{
    private readonly IOptionService _service;
    public OptionController(IOptionService baseService) : base(baseService)
    {
        this._service = baseService as IOptionService;
        this.UnwantedProperties += "";
        IncludesProperties = "";
    }
}
