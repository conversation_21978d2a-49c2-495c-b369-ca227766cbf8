﻿using UBEE.Service.Contracts;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;

namespace UBEE.Service;

public sealed class ImageService : IImageService
{
    public async Task<byte[]> ResizeAndOptimizeAsync(Stream inputStream, int width = 512, int height = 512, int quality = 85)
    {
        using var image = await Image.LoadAsync(inputStream);

        image.Mutate(x => x.Resize(new ResizeOptions
        {
            Size = new Size(width, height),
            Mode = ResizeMode.Pad,
            PadColor = Color.White
        }));

        using var outputStream = new MemoryStream();
        await image.SaveAsJpegAsync(outputStream, new JpegEncoder { Quality = quality });

        return outputStream.ToArray();
    }
}