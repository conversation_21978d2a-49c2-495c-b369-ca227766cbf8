﻿using UBEE.MODELS.Common.Models;

namespace UBEE.MODELS.DataSource.Models;

public class SensorReading: AuditEntity
    {
        public Guid IdCapteur { get; set; }

        public Capteur? Capteur { get; set; }

        public DateTime Timestamp { get; set; }

        // Common Zigbee2MQTT attributes
        public int? Battery { get; set; }

        public int? LinkQuality { get; set; }

        public double? Temperature { get; set; }

        public double? Humidity { get; set; }

        public double? Illuminance { get; set; }

        public double? Power { get; set; }

        public double? Energy { get; set; }

        public double? Voltage { get; set; }

        public double? Current { get; set; }

        public bool? Occupancy { get; set; }

        public bool? Tamper { get; set; }

        public bool? Contact { get; set; }

        // Z-Wave JS UI often uses "battery_level" instead of "battery"
        public int? BatteryLevel { get; set; }

        // Z-Wave multilevel sensor value could land in "value"
        public double? Value { get; set; }
    }
